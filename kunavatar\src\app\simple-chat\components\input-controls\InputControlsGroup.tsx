'use client';

import React from 'react';
import { ChatStyleControl, ChatStyle, DisplaySize } from './ChatStyleControl';
import { MemoryControl } from './MemoryControl';
import { ToolControl } from './ToolControl';
import { PromptOptimizeControl } from './PromptOptimizeControl';
import { ChatActionsControl } from './ChatActionsControl';

interface InputControlsGroupProps {
  // 聊天样式相关
  chatStyle: ChatStyle;
  displaySize?: DisplaySize;
  onChatStyleChange: (style: ChatStyle) => void;
  onDisplaySizeChange?: (size: DisplaySize) => void;
  
  // 工具相关
  enableTools: boolean;
  isCheckingModel: boolean;
  modelSupportsTools: boolean | null;
  showToolSettings: boolean;
  selectedToolsCount: number;
  onToolsToggle: () => void;
  onShowToolSettings: (show: boolean) => void;
  
  // 记忆相关
  isMemoryVisible?: boolean;
  onMemoryToggle?: () => void;
  conversationId?: number | null;
  
  // 提示词优化相关
  onInsertText: (text: string) => void;
  
  // 聊天操作相关
  onClearChat?: () => void;
}

export function InputControlsGroup({
  chatStyle,
  displaySize,
  onChatStyleChange,
  onDisplaySizeChange,
  enableTools,
  isCheckingModel,
  modelSupportsTools,
  showToolSettings,
  selectedToolsCount,
  onToolsToggle,
  onShowToolSettings,
  isMemoryVisible,
  onMemoryToggle,
  conversationId,
  onInsertText,
  onClearChat,
}: InputControlsGroupProps) {
  return (
    <div className="flex items-center gap-3">
      {/* 聊天样式控制 */}
      <ChatStyleControl
        chatStyle={chatStyle}
        displaySize={displaySize}
        onStyleChange={onChatStyleChange}
        onDisplaySizeChange={onDisplaySizeChange}
      />
      
      {/* 分隔线 */}
      <div className="w-px h-6 bg-[var(--color-border)]" />
      
      {/* 提示词优化控制 */}
      <PromptOptimizeControl
        onInsertText={onInsertText}
      />
      
      {/* 工具控制 */}
      <ToolControl
        enableTools={enableTools}
        isCheckingModel={isCheckingModel}
        modelSupportsTools={modelSupportsTools}
        showToolSettings={showToolSettings}
        selectedToolsCount={selectedToolsCount}
        onToolsToggle={onToolsToggle}
        onShowToolSettings={onShowToolSettings}
      />
      
      {/* 记忆控制 */}
      {onMemoryToggle && (
        <MemoryControl
          isMemoryVisible={isMemoryVisible || false}
          onMemoryToggle={onMemoryToggle}
          conversationId={conversationId || null}
        />
      )}
      
      {/* 清除聊天控制 */}
      {onClearChat && (
        <ChatActionsControl onClearChat={onClearChat} />
      )}
    </div>
  );
}