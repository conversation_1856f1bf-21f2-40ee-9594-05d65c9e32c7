# Markdown 样式系统

本项目包含一个完整的 Markdown 渲染样式系统，提供美观、现代化的 Markdown 内容显示。

## 特性

### 🎨 完整的样式支持
- **标题样式**: H1-H6 标题，带有独特的装饰元素
- **代码高亮**: 支持语法高亮和复制功能的代码块
- **表格样式**: 美观的响应式表格
- **列表样式**: 自定义的有序和无序列表
- **引用块**: 带有引号装饰的引用样式
- **链接效果**: 动画下划线效果
- **图片处理**: 圆角和阴影效果

### 🌓 主题支持
- 完全支持浅色/深色主题切换
- 自动适应系统主题色彩
- 平滑的主题切换过渡

### 📱 响应式设计
- 移动端优化
- 自适应布局
- 触摸友好的交互

### ⚡ 性能优化
- CSS-only 样式（无 JavaScript 依赖）
- 最小化的样式文件
- 流式渲染支持

## 使用方法

### 基础使用

```tsx
import { MarkdownRenderer } from '@/app/simple-chat/components/ui/MarkdownRenderer';

function MyComponent() {
  const markdownContent = `
# 标题
这是一段**加粗**的文字和*斜体*文字。

\`\`\`javascript
console.log('Hello, World!');
\`\`\`
  `;

  return (
    <MarkdownRenderer 
      content={markdownContent}
      className="my-custom-class"
    />
  );
}
```

### 流式渲染

```tsx
<MarkdownRenderer 
  content={streamingContent}
  isStreaming={true}
/>
```

### 自定义样式

组件会自动应用 `.markdown-renderer` 类，你可以通过 CSS 进行进一步自定义：

```css
.my-custom-markdown {
  /* 自定义容器样式 */
}

.my-custom-markdown h1 {
  /* 自定义标题样式 */
}
```

## 样式类说明

### 主要类名

- `.markdown-renderer` - 主容器类
- `.markdown-code-block` - 代码块容器
- `.markdown-code-header` - 代码块标题栏
- `.markdown-code-copy-btn` - 复制按钮
- `.markdown-inline-code` - 内联代码
- `.markdown-table-wrapper` - 表格包装器

### 代码块特性

代码块包含以下增强功能：

1. **语言标识**: 自动显示编程语言
2. **复制按钮**: 一键复制代码
3. **语法高亮**: 使用 Prism.js 进行语法高亮
4. **主题适配**: 自动适应浅色/深色主题

### 表格特性

- 自动横向滚动
- 悬停行高亮
- 响应式布局
- 美观的边框和阴影

## 主题变量

样式系统使用 CSS 自定义属性，可以通过修改这些变量来自定义外观：

```css
:root {
  --color-primary: #6a5ac2;
  --color-background: #ffffff;
  --color-foreground: #111111;
  --color-border: #e5e5e5;
  /* 更多变量... */
}
```

## 最佳实践

1. **性能**: 避免频繁重新渲染，使用 `React.memo` 包装组件
2. **可访问性**: 确保代码块有适当的语言标识
3. **响应式**: 测试不同屏幕尺寸下的显示效果
4. **主题**: 确保自定义样式在两种主题下都正常显示

## 文件结构

```
src/styles/
├── markdown.css          # 主要 Markdown 样式
└── README.md            # 本文档

src/app/simple-chat/components/ui/
├── MarkdownRenderer.tsx  # React 组件
└── StreamedContent.tsx  # 流式内容组件
```

## 支持的 Markdown 语法

- [x] 标题 (H1-H6)
- [x] 段落和换行
- [x] **加粗** 和 *斜体*
- [x] `内联代码`
- [x] 代码块（带语法高亮）
- [x] 链接
- [x] 图片
- [x] 列表（有序和无序）
- [x] 引用块
- [x] 表格
- [x] ~~删除线~~
- [x] 分割线
- [x] 任务列表
- [x] HTML 标签（安全的子集）

## 故障排除

### 样式不生效
确保 `markdown.css` 被正确导入到 `globals.css` 中：

```css
@import '../styles/markdown.css';
```

### 代码高亮不工作
检查 `react-syntax-highlighter` 是否正确安装和导入。

### 主题切换异常
确保主题变量在 `:root` 和 `.dark` 类中都有定义。 