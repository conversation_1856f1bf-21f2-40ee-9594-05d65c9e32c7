import { NextRequest, NextResponse } from 'next/server';
import { CustomModelService, CustomModel } from '@/lib/database/custom-models';
import { OllamaClient } from '@/lib/ollama';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const forceSync = searchParams.get('sync') === 'true';
    
    // 只在强制同步或首次加载时同步Ollama模型
    if (forceSync) {
      const ollamaClient = new OllamaClient();
      const ollamaModels = await ollamaClient.getModels();
      CustomModelService.syncWithOllama(ollamaModels);
    }

    // 从数据库获取所有模型
    const search = searchParams.get('search') || undefined;
    const tags = searchParams.get('tags')?.split(',') || undefined;
    const sortBy = searchParams.get('sortBy') as any || 'ollama_modified_at';
    const sortOrder = searchParams.get('sortOrder') as any || 'desc';
    
    const models = CustomModelService.getAll({
      search,
      tags,
      sortBy,
      sortOrder
    });

    return NextResponse.json({ success: true, models });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('API Error in GET /api/custom-models:', error);
    return NextResponse.json({ success: false, error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const model = CustomModelService.create(body);
    
    return NextResponse.json({
      success: true,
      model,
    }, { status: 201 });
  } catch (error) {
    console.error('创建自定义模型失败:', error);
    return NextResponse.json(
      {
        error: '创建模型失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 400 }
    );
  }
}