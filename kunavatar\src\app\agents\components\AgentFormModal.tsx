'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AgentWithRelations } from '../types';
import { CustomModel } from '@/lib/database/custom-models';
import { McpServer, McpTool } from '@/lib/database';
import { ModelSelector } from '@/components/ModelSelector';
import { z } from 'zod';
import { AlertCircle, Bot, Server, Axe, X, Sparkles, Upload, Camera, Brain, Settings, ToggleLeft, ToggleRight, RotateCcw } from 'lucide-react';

interface AgentFormModalProps {
  agent: AgentWithRelations | null;
  onClose: () => void;
  onSave: () => void;
  availableModels: CustomModel[];
  availableServers: McpServer[];
  allAvailableTools: McpTool[];
}

const agentSchema = z.object({
  name: z.string().min(1, '智能体名称不能为空'),
  model_id: z.coerce.number().int().positive({ message: '必须选择一个搭载模型' }),
  description: z.string().optional(),
  avatar: z.string().optional(),
  server_ids: z.array(z.number()),
  tool_ids: z.array(z.number()),
});

// 表单区域组件
const FormSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <div className="space-y-6">
    <h3 className="section-title !text-theme-foreground-muted">{title}</h3>
    {children}
  </div>
);

// 表单输入组件
const FormInput = ({ 
  label, 
  required = false, 
  error,
  hint,
  children
}: { 
  label: string; 
  required?: boolean;
  error?: string;
  hint?: string;
  children: React.ReactNode;
}) => (
  <div className="space-y-2">
    <label className="text-sm font-medium text-theme-foreground block">
      {label}
      {required && <span className="text-theme-error ml-1">*</span>}
    </label>
    {children}
    {error && (
      <p className="text-sm text-theme-error">{error}</p>
    )}
    {hint && !error && (
      <p className="text-xs text-theme-foreground-muted">{hint}</p>
    )}
  </div>
);

// 头像上传组件
const AvatarUpload = ({ 
  currentAvatar, 
  onAvatarChange 
}: { 
  currentAvatar: string | null; 
  onAvatarChange: (avatar: string | null) => void; 
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatar);

  // 当currentAvatar变化时更新previewUrl
  useEffect(() => {
    setPreviewUrl(currentAvatar);
  }, [currentAvatar]);

  // 图片压缩函数
  const compressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // 设置最大尺寸
        const maxSize = 200;
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        ctx?.drawImage(img, 0, 0, width, height);
        
        // 压缩质量
        const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.8);
        resolve(compressedDataUrl);
      };
      
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }
    
    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('图片文件不能超过5MB');
      return;
    }
    
    setIsUploading(true);
    
    try {
      const compressedImage = await compressImage(file);
      
      // 上传到服务器
      const formData = new FormData();
      
      // 将base64转换为blob
      const response = await fetch(compressedImage);
      const blob = await response.blob();
      formData.append('avatar', blob, `avatar_${Date.now()}.jpg`);
      
      const uploadResponse = await fetch('/api/upload/avatar', {
        method: 'POST',
        body: formData,
      });
      
      if (!uploadResponse.ok) {
        throw new Error('上传失败');
      }
      
      const { url } = await uploadResponse.json();
      setPreviewUrl(url);
      onAvatarChange(url);
    } catch (error) {
      console.error('头像上传失败:', error);
      alert('头像上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveAvatar = () => {
    setPreviewUrl(null);
    onAvatarChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="flex items-center gap-4">
      <div className="relative">
        <div className="w-20 h-20 rounded-xl bg-gradient-to-br from-theme-primary to-theme-accent flex items-center justify-center overflow-hidden border-2 border-theme-border">
          {previewUrl ? (
            <img 
              src={previewUrl} 
              alt="头像预览" 
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error('头像预览加载失败:', previewUrl);
                console.log('错误详情:', e);
              }}
            />
          ) : (
            <Bot className="w-10 h-10 text-white" />
          )}
        </div>
        {previewUrl && (
          <button
            type="button"
            onClick={handleRemoveAvatar}
            className="absolute -top-2 -right-2 w-6 h-6 bg-theme-error text-white rounded-full flex items-center justify-center text-xs hover:bg-theme-error/80 transition-colors"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </div>
      
      <div className="flex-1">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="btn-base btn-secondary flex items-center gap-2 text-sm"
        >
          {isUploading ? (
            <>
              <div className="w-4 h-4 border-2 border-theme-primary border-t-transparent rounded-full animate-spin" />
              上传中...
            </>
          ) : (
            <>
              <Camera className="w-4 h-4" />
              {previewUrl ? '更换头像' : '上传头像'}
            </>
          )}
        </button>
        <p className="text-xs text-theme-foreground-muted mt-1">
          支持 JPG、PNG 格式，建议尺寸 200x200px，最大 5MB
        </p>
      </div>
    </div>
  );
};

// 多选复选框组件
const MultiSelectCheckbox = ({ 
  options, 
  selectedIds, 
  onChange, 
  disabled = false,
  keyPrefix = 'item' 
}: {
  options: { id: number; name: string }[];
  selectedIds: number[];
  onChange: (selectedIds: number[]) => void;
  disabled?: boolean;
  keyPrefix?: string;
}) => {
  const handleCheckboxChange = (id: number) => {
    if (disabled) return;
    
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      onChange([...selectedIds, id]);
    }
  };

  return (
    <div className="space-y-3">
      {options.map(option => (
        <label 
          key={`${keyPrefix}-${option.id}`}
          className={`flex items-center gap-3 p-3 rounded-lg border transition-colors cursor-pointer ${
            selectedIds.includes(option.id)
              ? 'bg-theme-primary/10 border-theme-primary/30'
              : 'bg-theme-card border-theme-border hover:bg-theme-card-hover'
          } ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
        >
          <input
            type="checkbox"
            checked={selectedIds.includes(option.id)}
            onChange={() => handleCheckboxChange(option.id)}
            disabled={disabled}
            className="w-4 h-4 text-theme-primary border-theme-border rounded focus:ring-theme-primary"
          />
          <span className="text-sm font-medium text-theme-foreground">
            {option.name}
          </span>
        </label>
      ))}
    </div>
  );
};

// 服务器和工具选择组件
const ServerToolSelector = ({
  availableServers,
  allAvailableTools,
  selectedServerIds,
  selectedToolIds,
  onServerChange,
  onToolChange,
  maxTools = 10
}: {
  availableServers: McpServer[];
  allAvailableTools: McpTool[];
  selectedServerIds: number[];
  selectedToolIds: number[];
  onServerChange: (serverIds: number[]) => void;
  onToolChange: (toolIds: number[]) => void;
  maxTools?: number;
}) => {
  const handleServerToggle = (serverId: number) => {
    const isSelected = selectedServerIds.includes(serverId);
    let newServerIds: number[];
    
    if (isSelected) {
      newServerIds = selectedServerIds.filter(id => id !== serverId);
      // 移除该服务器下的所有工具
      const toolsToRemove = allAvailableTools
        .filter(tool => tool.server_id === serverId)
        .map(tool => tool.id);
      const newToolIds = selectedToolIds.filter(toolId => !toolsToRemove.includes(toolId));
      onToolChange(newToolIds);
    } else {
      newServerIds = [...selectedServerIds, serverId];
    }
    
    onServerChange(newServerIds);
  };

  const handleToolToggle = (toolId: number) => {
    const isSelected = selectedToolIds.includes(toolId);
    
    if (isSelected) {
      onToolChange(selectedToolIds.filter(id => id !== toolId));
    } else {
      if (selectedToolIds.length < maxTools) {
        onToolChange([...selectedToolIds, toolId]);
      }
    }
  };

  return (
    <div className="space-y-4">
      {availableServers.map(server => {
        const isServerSelected = selectedServerIds.includes(server.id);
        const serverTools = allAvailableTools.filter(tool => tool.server_id === server.id);
        
        return (
          <div key={server.id} className="border border-theme-border rounded-lg overflow-hidden">
            {/* 服务器选择 */}
            <label className={`flex items-center gap-3 p-4 cursor-pointer transition-colors ${
              isServerSelected
                ? 'bg-theme-primary/10 border-theme-primary/30'
                : 'bg-theme-card hover:bg-theme-card-hover'
            }`}>
              <input
                type="checkbox"
                checked={isServerSelected}
                onChange={() => handleServerToggle(server.id)}
                className="w-4 h-4 text-theme-primary border-theme-border rounded focus:ring-theme-primary"
              />
              <Server className="w-5 h-5 text-theme-foreground-muted" />
              <div className="flex-1">
                <span className="text-sm font-medium text-theme-foreground">
                  {server.display_name}
                </span>
                {serverTools.length > 0 && (
                  <p className="text-xs text-theme-foreground-muted mt-1">
                    包含 {serverTools.length} 个工具
                  </p>
                )}
              </div>
            </label>
            
            {/* 工具列表 */}
            {isServerSelected && serverTools.length > 0 && (
              <div className="border-t border-theme-border bg-theme-background-secondary">
                <div className="p-3">
                  <div className="flex items-center gap-2 mb-3 text-xs text-theme-foreground-muted">
                    <Axe className="w-3 h-3" />
                    <span>选择工具 ({selectedToolIds.filter(id => serverTools.some(t => t.id === id)).length}/{serverTools.length})</span>
                  </div>
                  <div className="space-y-2">
                    {serverTools.map(tool => {
                      const isToolSelected = selectedToolIds.includes(tool.id);
                      const isDisabled = !isToolSelected && selectedToolIds.length >= maxTools;
                      
                      return (
                        <label 
                          key={tool.id}
                          className={`flex items-center gap-2 p-2 rounded border text-xs cursor-pointer transition-colors ${
                            isToolSelected
                              ? 'bg-theme-primary/5 border-theme-primary/20 text-theme-primary'
                              : isDisabled
                              ? 'bg-theme-card border-theme-border text-theme-foreground-muted cursor-not-allowed opacity-50'
                              : 'bg-theme-card border-theme-border text-theme-foreground hover:bg-theme-card-hover'
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={isToolSelected}
                            onChange={() => handleToolToggle(tool.id)}
                            disabled={isDisabled}
                            className="w-3 h-3 text-theme-primary border-theme-border rounded focus:ring-theme-primary"
                          />
                          <span className="font-medium">{tool.name}</span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

const AgentFormModal: React.FC<AgentFormModalProps> = ({
  agent,
  onClose,
  onSave,
  availableModels,
  availableServers,
  allAvailableTools,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    model_id: null as number | null,
    avatar: null as string | null,
    server_ids: [] as number[],
    tool_ids: [] as number[],
  });
  
  const [initialFormData, setInitialFormData] = useState({
    name: '',
    description: '',
    model_id: null as number | null,
    avatar: null as string | null,
    server_ids: [] as number[],
    tool_ids: [] as number[],
  });
  
  // 记忆设置状态（简化为只有启用开关）
  const [memoryEnabled, setMemoryEnabled] = useState(true);
  const [memoryLoading, setMemoryLoading] = useState(false);
  const [errors, setErrors] = useState<z.ZodError | null>(null);
  
  const [isSaving, setIsSaving] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  const isEditMode = agent !== null;
  
  // 本地缓存键名
  const CACHE_KEY = 'agent_form_cache';
  
  // 保存到本地缓存
  const saveToCache = useCallback((data: typeof formData) => {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('保存到本地缓存失败:', error);
    }
  }, []);
  
  // 从本地缓存加载
  const loadFromCache = useCallback(() => {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('从本地缓存加载失败:', error);
    }
    return null;
  }, []);
  
  // 清除本地缓存
  const clearCache = useCallback(() => {
    try {
      localStorage.removeItem(CACHE_KEY);
    } catch (error) {
      console.error('清除本地缓存失败:', error);
    }
  }, []);
  
  // 重置表单到初始状态
  const resetForm = useCallback(() => {
    setFormData({ ...initialFormData });
    setMemoryEnabled(true);
    clearCache();
  }, [initialFormData, clearCache]);
  
  // 关闭弹窗时的处理
  const handleClose = useCallback(() => {
    // 如果不是编辑模式且有未保存的数据，保持缓存
    // 只有在编辑模式或用户主动重置时才清除缓存
    onClose();
  }, [onClose]);
  
  // ESC键关闭弹窗
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleClose]);
  
  // 监听表单数据变化，自动保存到缓存
  useEffect(() => {
    if (!isEditMode) {
      saveToCache(formData);
    }
  }, [formData, isEditMode, saveToCache]);

  useEffect(() => {
    if (isEditMode && agent) {
      const editData = {
        name: agent.name,
        description: agent.description || '',
        model_id: agent.model_id,
        avatar: agent.avatar || null,
        server_ids: agent.servers.map(s => s.id),
        tool_ids: agent.tools.map(t => t.id),
      };
      setFormData(editData);
      setInitialFormData(editData);
      
      // 加载记忆设置
      loadMemorySettings(agent.id);
    } else {
      const defaultData = {
        name: '', 
        description: '', 
        model_id: null, 
        avatar: null,
        server_ids: [], 
        tool_ids: [],
      };
      
      // 尝试从缓存加载数据
      const cachedData = loadFromCache();
      if (cachedData) {
        setFormData(cachedData);
      } else {
        setFormData(defaultData);
      }
      setInitialFormData(defaultData);
      
      // 重置记忆设置为默认值
      setMemoryEnabled(true);
    }
  }, [agent, isEditMode]);

  // 加载记忆设置
  const loadMemorySettings = async (agentId: number) => {
    setMemoryLoading(true);
    try {
      const response = await fetch(`/api/agents/${agentId}/memory`);
      if (response.ok) {
        const { memorySettings: settings } = await response.json();
        setMemoryEnabled(settings.memory_enabled);
      }
    } catch (error) {
      console.error('加载记忆设置失败:', error);
    } finally {
      setMemoryLoading(false);
    }
  };
  


  const handleSubmit = async () => {
    setErrors(null);
    setApiError(null);
    const result = agentSchema.safeParse(formData);
    if (!result.success) {
      setErrors(result.error);
      setApiError('请检查表单中的错误信息');
      return;
    }

    setIsSaving(true);
    
    try {
      // 保存智能体基本信息
      const response = await fetch(isEditMode ? `/api/agents/${agent!.id}` : '/api/agents', {
        method: isEditMode ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(result.data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '保存智能体失败');
      }

      const agentData = await response.json();
      const agentId = isEditMode ? agent!.id : agentData.id;

      // 保存记忆设置
      const memoryResponse = await fetch(`/api/agents/${agentId}/memory`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ memory_enabled: memoryEnabled }),
      });

      if (!memoryResponse.ok) {
        console.warn('记忆设置保存失败，但智能体已创建/更新');
      }

      // 成功保存后清除缓存
      if (!isEditMode) {
        clearCache();
      }
      
      onSave();
    } catch (err) {
      setApiError(err instanceof Error ? err.message : '发生未知错误');
    } finally {
      setIsSaving(false);
    }
  };



  const getFieldError = (fieldName: string) => {
    return errors?.formErrors.fieldErrors[fieldName]?.[0];
  };

  return (
    <AnimatePresence>
      <motion.div 
        className="fixed inset-0 bg-black/40 backdrop-blur-sm flex justify-center items-start z-50 p-4 pt-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        // 移除点击外部区域关闭弹窗的功能
      >
        <motion.div 
          className="bg-theme-background rounded-2xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden border border-theme-border"
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.95, opacity: 0, y: -10 }}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-8 pb-6 border-b border-theme-border">
            <div className="flex items-center gap-3">
              <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-theme-primary to-theme-accent flex items-center justify-center">
                <Bot className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="page-title text-theme-foreground">
                  {isEditMode ? '编辑智能体' : '创建智能体'}
                </h2>
                <p className="text-theme-foreground-muted text-sm">
                  配置智能体的模型、工具和行为特征
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="w-10 h-10 rounded-full bg-theme-card hover:bg-theme-card-hover flex items-center justify-center text-theme-foreground-muted hover:text-theme-foreground transition-all duration-200 border border-theme-border"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* 表单内容 */}
          <div className="flex-1 overflow-y-auto scrollbar-thin p-8 space-y-8">
            {/* 错误提示 */}
            {apiError && (
              <div className="flex items-start gap-3 p-4 bg-theme-error/10 border border-theme-error/20 text-theme-error rounded-lg">
                <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">操作失败</p>
                  <p className="text-sm mt-1">{apiError}</p>
                </div>
              </div>
            )}
            
            {/* 基本信息 */}
            <FormSection title="基本信息">
              <div className="grid grid-cols-1 gap-6">
                <FormInput 
                  label="头像" 
                  error={getFieldError('avatar')}
                >
                  <AvatarUpload
                    currentAvatar={formData.avatar}
                    onAvatarChange={(avatar) => setFormData(prev => ({ ...prev, avatar }))}
                  />
                </FormInput>
                
                <FormInput 
                  label="智能体名称" 
                  required 
                  error={getFieldError('name')}
                >
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="form-input-base"
                    placeholder="例如：客服助手、数据分析师..."
                  />
                </FormInput>
                
                <FormInput 
                  label="描述" 
                  error={getFieldError('description')}
                >
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="form-input-base h-20 resize-none"
                    placeholder="描述这个智能体的主要功能和使用场景..."
                  />
                </FormInput>
              </div>
            </FormSection>

            {/* 模型配置 */}
            <FormSection title="模型配置">
              <div className="grid grid-cols-1 gap-6">
                <FormInput 
                  label="搭载模型" 
                  required 
                  error={getFieldError('model_id')}
                >
                  <ModelSelector
                    models={availableModels.map(model => ({
                      name: model.base_model,
                      model: model.base_model,
                      modified_at: new Date().toISOString(),
                      size: 0,
                      digest: '',
                      details: {
                        parent_model: '',
                        format: '',
                        family: model.family || model.base_model,
                        families: [],
                        parameter_size: '',
                        quantization_level: '',
                      }
                    }))}
                    selectedModel={formData.model_id ? availableModels.find(m => m.id === formData.model_id)?.base_model || '' : ''}
                    onModelChange={(modelName: string) => {
                      const selectedModel = availableModels.find(m => m.base_model === modelName);
                      setFormData(prev => ({ ...prev, model_id: selectedModel?.id || null }));
                    }}
                    customModels={availableModels.map(m => ({
                      base_model: m.base_model,
                      display_name: m.display_name,
                      family: m.family
                    }))}
                  />
                </FormInput>
                

              </div>
            </FormSection>
            
            {/* 工具配置 */}
            <FormSection title="工具配置">
              <div className="space-y-6">
                <FormInput 
                  label="MCP服务器和工具" 
                  hint={`选择智能体可以连接的服务器和使用的工具 (已选择 ${formData.server_ids.length} 个服务器，${formData.tool_ids.length}/10 个工具)`}
                >
                  {availableServers.length > 0 ? (
                    <div className="space-y-4">
                      <ServerToolSelector
                        availableServers={availableServers}
                        allAvailableTools={allAvailableTools}
                        selectedServerIds={formData.server_ids}
                        selectedToolIds={formData.tool_ids}
                        onServerChange={(serverIds) => {
                          setFormData(prev => ({ ...prev, server_ids: serverIds }));
                        }}
                        onToolChange={(toolIds) => {
                          if (toolIds.length > 10) {
                            setApiError("最多只能选择10个工具");
                            return;
                          }
                          setApiError(null);
                          setFormData(prev => ({ ...prev, tool_ids: toolIds }));
                        }}
                        maxTools={10}
                      />
                      {formData.tool_ids.length >= 10 && (
                        <p className="text-sm text-theme-warning flex items-center gap-2">
                          <AlertCircle className="w-4 h-4" />
                          已达到最大工具数量限制
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="p-4 bg-theme-background-tertiary border border-theme-border rounded-lg">
                      <div className="flex items-center gap-2 text-theme-foreground-muted">
                        <AlertCircle className="w-5 h-5" />
                        <p className="text-sm">暂无可用的MCP服务器</p>
                      </div>
                    </div>
                  )}
                </FormInput>
              </div>
            </FormSection>

            {/* 记忆设置 */}
            <FormSection title="记忆设置">
              <div className="space-y-6">
                {memoryLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-theme-primary"></div>
                    <span className="ml-2 text-theme-foreground-muted">加载记忆设置...</span>
                  </div>
                ) : (
                  <FormInput 
                    label="启用记忆功能"
                    hint="开启后，智能体将自动总结对话历史，节省上下文空间。详细配置请前往设置页面。"
                  >
                    <div className="flex items-center gap-3">
                      <button
                        type="button"
                        onClick={() => setMemoryEnabled(!memoryEnabled)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${memoryEnabled ? 'bg-theme-primary' : 'bg-gray-200'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${memoryEnabled ? 'translate-x-6' : 'translate-x-1'}`}
                        />
                      </button>
                      <span className="text-sm text-theme-foreground-muted">
                        {memoryEnabled ? '已启用' : '已关闭'}
                      </span>
                      <Brain className={`w-4 h-4 ${memoryEnabled ? 'text-theme-primary' : 'text-gray-400'}`} />
                    </div>
                  </FormInput>
                )}
              </div>
            </FormSection>
          </div>

          {/* 底部操作 */}
          <div className="p-4 flex justify-between items-center border-t border-theme-border bg-theme-background-secondary">
            <div className="flex items-center gap-3">
              {!isEditMode && (
                <button
                  onClick={resetForm}
                  className="btn-base text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-background-secondary border border-theme-border px-4 py-2 flex items-center gap-2"
                  title="重置表单"
                >
                  <RotateCcw className="w-4 h-4" />
                  重置
                </button>
              )}
            </div>
            <div className="flex items-center gap-3">
               <button
                 onClick={handleClose}
                 className="btn-base btn-secondary px-6 py-3"
               >
                 取消
               </button>
              <button
                onClick={handleSubmit}
                disabled={isSaving}
                className="btn-base btn-primary px-6 py-3"
              >
                <Sparkles className="w-4 h-4" />
                {isSaving ? '保存中...' : (isEditMode ? '更新' : '创建')}
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AgentFormModal;