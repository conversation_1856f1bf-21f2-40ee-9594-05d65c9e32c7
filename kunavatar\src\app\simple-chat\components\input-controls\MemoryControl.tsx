import React from 'react';
import { <PERSON> } from 'lucide-react';
import { BaseControlButton } from './BaseControlButton';

interface MemoryControlProps {
  isMemoryVisible: boolean;
  onMemoryToggle: () => void;
  conversationId: number | null;
}

export function MemoryControl({ 
  isMemoryVisible, 
  onMemoryToggle, 
  conversationId 
}: MemoryControlProps) {
  const isDisabled = !conversationId;
  
  return (
    <BaseControlButton
      active={isMemoryVisible}
      disabled={isDisabled}
      onClick={onMemoryToggle}
      tooltip={
        isDisabled 
          ? "请先开始对话以查看记忆" 
          : isMemoryVisible 
            ? "隐藏对话记忆" 
            : "显示对话记忆"
      }
    >
      <Brain className="w-4 h-4" />
    </BaseControlButton>
  );
} 