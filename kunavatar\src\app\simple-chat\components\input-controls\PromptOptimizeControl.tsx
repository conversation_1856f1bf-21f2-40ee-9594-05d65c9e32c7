'use client';

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Send, Co<PERSON>, RotateCcw, ArrowDown } from 'lucide-react';
import { BaseControlButton } from './BaseControlButton';
import { usePromptOptimizeSettings } from '../../../settings/hooks/usePromptOptimizeSettings';

interface PromptOptimizeControlProps {
  onInsertText: (text: string) => void;
}

export interface OptimizeResult {
  originalText: string;
  optimizedText: string;
  timestamp: number;
}

export function PromptOptimizeControl({ onInsertText }: PromptOptimizeControlProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputText, setInputText] = useState('');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizeResult, setOptimizeResult] = useState<OptimizeResult | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  
  const { settings } = usePromptOptimizeSettings();
  const panelRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // 点击外部关闭面板
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // 面板打开时聚焦到输入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 重置状态
  const resetState = () => {
    setInputText('');
    setOptimizeResult(null);
    setCopySuccess(false);
  };

  // 处理提示词优化
  const handleOptimize = async () => {
    if (!inputText.trim() || !settings.promptEnabled || !settings.promptModel) {
      return;
    }

    setIsOptimizing(true);
    try {
      const response = await fetch('/api/prompt-optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText.trim(),
          model: settings.promptModel,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setOptimizeResult({
          originalText: inputText.trim(),
          optimizedText: data.optimizedText,
          timestamp: Date.now(),
        });
      } else {
        console.error('提示词优化失败:', data.error);
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('提示词优化请求失败:', error);
      // TODO: 显示错误提示
    } finally {
      setIsOptimizing(false);
    }
  };

  // 复制到剪贴板
  const handleCopy = async () => {
    if (!optimizeResult) return;

    try {
      await navigator.clipboard.writeText(optimizeResult.optimizedText);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 插入到主输入框
  const handleInsert = () => {
    if (!optimizeResult) return;
    onInsertText(optimizeResult.optimizedText);
    setIsOpen(false);
    resetState();
  };

  // 按钮禁用状态
  const isDisabled = !settings.promptEnabled || !settings.promptModel;

  return (
    <div className="relative">
      {/* 控制按钮 */}
      <BaseControlButton
        onClick={() => {
          if (isDisabled) return;
          setIsOpen(!isOpen);
          if (!isOpen) {
            resetState();
          }
        }}
        active={isOpen}
        disabled={isDisabled}
        tooltip={isDisabled ? "请先在设置中启用提示词优化功能" : "提示词优化"}
        statusIndicator={isDisabled ? { status: 'warning', position: 'top-right' } : undefined}
      >
        <Sparkles className="w-4 h-4" />
      </BaseControlButton>

      {/* 优化面板 */}
      {isOpen && (
        <div 
          ref={panelRef}
          className="absolute bottom-full left-0 mb-2 w-[500px] bg-[var(--color-card)] border border-[var(--color-border)] rounded-[var(--radius-lg)] z-50"
        >
          {/* 面板头部 */}
          <div className="p-4 border-b border-[var(--color-border)]">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-[var(--color-primary)]" />
              <span className="text-sm font-medium text-[var(--color-foreground)]">提示词优化</span>
            </div>
          </div>

          {/* 输入区域 */}
          <div className="p-4 space-y-3">
            {/* 优化结果显示 */}
            {optimizeResult && (
              <div className="border border-[var(--color-border)] rounded-[var(--radius-lg)]">
                <div className="flex items-center justify-between p-3 border-b border-[var(--color-border)] bg-[var(--color-background-secondary)]">
                  <span className="text-xs font-medium text-[var(--color-foreground-muted)]">优化结果</span>
                  <div className="flex gap-1">
                    <button
                      onClick={handleCopy}
                      className="p-1 text-[var(--color-foreground-muted)] hover:text-[var(--color-foreground)] hover:bg-[var(--color-background)] rounded-[var(--radius-sm)] transition-colors"
                      title="复制"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                    <button
                      onClick={handleInsert}
                      className="p-1 text-[var(--color-primary)] hover:bg-[var(--color-primary)]/10 rounded-[var(--radius-sm)] transition-colors"
                      title="插入到输入框"
                    >
                      <ArrowDown className="w-3 h-3" />
                    </button>
                    <button
                      onClick={resetState}
                      className="p-1 text-[var(--color-foreground-muted)] hover:text-[var(--color-foreground)] hover:bg-[var(--color-background)] rounded-[var(--radius-sm)] transition-colors"
                      title="清除"
                    >
                      <RotateCcw className="w-3 h-3" />
                    </button>
                  </div>
                </div>
                <div className="p-3 max-h-72 overflow-y-auto scrollbar-thin">
                  <p className="text-sm text-[var(--color-foreground)] whitespace-pre-wrap leading-relaxed">
                    {optimizeResult.optimizedText}
                  </p>
                  {copySuccess && (
                    <div className="text-xs text-[var(--color-success)] mt-2">
                      已复制到剪贴板
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <textarea
                ref={inputRef}
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="输入需要优化的提示词..."
                className="flex-1 px-3 py-2 text-sm border border-[var(--color-border)] rounded-[var(--radius-lg)] bg-[var(--color-background)] text-[var(--color-foreground)] placeholder-[var(--color-foreground-muted)] focus:ring-2 focus:ring-[var(--color-primary)]/30 focus:border-[var(--color-primary)] resize-none"
                rows={3}
                disabled={isOptimizing}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleOptimize();
                  }
                }}
              />
              <button
                onClick={handleOptimize}
                disabled={!inputText.trim() || isOptimizing}
                className="px-3 py-2 bg-[var(--color-primary)] text-white rounded-[var(--radius-lg)] hover:bg-[var(--color-primary-hover)] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isOptimizing ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}