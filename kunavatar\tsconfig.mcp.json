{"compilerOptions": {"target": "es2020", "module": "es2020", "lib": ["es2020"], "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", ".next"], "ts-node": {"esm": true, "compilerOptions": {"module": "es2020", "target": "es2020", "moduleResolution": "node"}}}