# 用户管理模块 API 文档

## 概述

用户管理模块提供了完整的用户认证、授权和管理功能，包括用户注册、登录、角色权限管理等。该模块采用基于角色的访问控制（RBAC）系统，支持细粒度的权限管理，确保系统安全性和可扩展性。

### 核心特性

- 🔐 **JWT认证**: 安全的令牌认证机制
- 👥 **角色权限**: 灵活的RBAC权限控制系统
- 🛡️ **安全防护**: 密码加密、输入验证、防护攻击
- 📱 **响应式UI**: 现代化的管理界面
- 🔧 **自动初始化**: 一键初始化超级管理员
- 📊 **完整监控**: 用户状态、登录记录、权限审计

## 认证方式

API 使用 JWT (JSON Web Token) 进行认证。需要在请求头中包含：

```
Authorization: Bearer <access_token>
```

## 权限系统

### 权限格式

系统采用 `resource:action` 格式的权限命名规范：

- **资源类型**: `users`, `roles`, `permissions`, `conversations`, `agents`
- **操作类型**: `create`, `read`, `update`, `delete`, `manage`
- **权限示例**: `users:read`, `users:create`, `users:manage`

### 特殊权限

- `resource:manage` - 拥有该资源的所有操作权限
- 超级管理员默认拥有所有资源的 `manage` 权限

### 权限检查逻辑

1. 检查用户是否拥有 `resource:action` 权限
2. 如果没有，检查是否拥有 `resource:manage` 权限
3. 权限通过角色分配给用户，支持多角色

## 系统初始化

### 超级管理员初始化

首次部署时，可使用初始化脚本创建超级管理员：

```bash
# 初始化超级管理员
node scripts/init-admin.js

# 强制重新初始化（会覆盖现有数据）
node scripts/init-admin.js --force
```

**默认超级管理员信息：**
- 用户名: `admin`
- 邮箱: `<EMAIL>`
- 默认密码: `123456`
- 角色: `super_admin`

**⚠️ 重要提醒：**
- 首次登录后请立即修改默认密码
- 生产环境中请使用强密码
- 建议启用邮箱验证功能

## 响应格式

所有 API 响应都遵循统一格式：

```json
{
  "success": true|false,
  "data": {...},      // 成功时返回
  "error": "...",     // 失败时返回
  "message": "..."    // 可选的消息
}
```

## 认证相关 API

### 用户注册

**POST** `/api/auth/register`

注册新用户账户。

**请求体：**
```json
{
  "username": "string",        // 必需，3-50字符，只能包含字母数字下划线
  "email": "string",           // 必需，有效邮箱地址
  "password": "string",        // 必需，6-128字符
  "confirmPassword": "string", // 必需，必须与password相同
  "first_name": "string",      // 可选，最大50字符
  "last_name": "string"        // 可选，最大50字符
}
```

**响应：**
```json
{
  "success": true,
  "message": "用户注册成功",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "status": "active",
    "email_verified": false,
    "created_at": "2025-07-03T00:00:00.000Z"
  }
}
```

### 用户登录

**POST** `/api/auth/login`

用户登录获取访问令牌。

**请求体：**
```json
{
  "username": "string",  // 用户名或邮箱
  "password": "string"   // 密码
}
```

**响应：**
```json
{
  "success": true,
  "message": "登录成功",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "status": "active"
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**注意：** 刷新令牌会通过 HTTP-only Cookie 设置。

### 获取当前用户信息

**GET** `/api/auth/me`

获取当前登录用户的详细信息。

**需要认证：** 是

**响应：**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "first_name": "Admin",
    "last_name": "User",
    "status": "active",
    "email_verified": false,
    "roles": [
      {
        "id": 1,
        "name": "admin",
        "display_name": "管理员"
      }
    ],
    "permissions": [
      {
        "id": 1,
        "name": "users:create",
        "display_name": "创建用户",
        "resource": "users",
        "action": "create"
      }
    ]
  }
}
```

### 刷新访问令牌

**POST** `/api/auth/refresh`

使用刷新令牌获取新的访问令牌。

**响应：**
```json
{
  "success": true,
  "message": "令牌刷新成功",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 用户登出

**POST** `/api/auth/logout`

登出当前用户，撤销刷新令牌。

**响应：**
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 忘记密码

**POST** `/api/auth/forgot-password`

请求密码重置。

**请求体：**
```json
{
  "email": "string"  // 用户邮箱
}
```

**响应：**
```json
{
  "success": true,
  "message": "密码重置链接已发送到您的邮箱"
}
```

### 重置密码

**POST** `/api/auth/reset-password`

使用重置令牌重置密码。

**请求体：**
```json
{
  "token": "string",           // 重置令牌
  "password": "string",        // 新密码
  "confirmPassword": "string"  // 确认新密码
}
```

**GET** `/api/auth/reset-password?token=<token>`

验证重置令牌是否有效。

## 用户管理 API

### 获取用户列表

**GET** `/api/users`

获取用户列表，支持分页、搜索和筛选。

**需要认证：** 是  
**需要权限：** `users:read`

**查询参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20，最大100）
- `search`: 搜索用户名或邮箱
- `status`: 筛选状态（active/inactive/suspended）
- `sort_by`: 排序字段（username/email/created_at/last_login_at）
- `sort_order`: 排序方向（asc/desc）

**响应：**
```json
{
  "success": true,
  "users": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User",
      "status": "active",
      "email_verified": false,
      "created_at": "2025-07-03T00:00:00.000Z",
      "last_login_at": "2025-07-03T01:00:00.000Z",
      "roles": [
        {
          "id": 1,
          "name": "admin",
          "display_name": "管理员"
        }
      ],
      "permissions": [...]
    }
  ],
  "stats": {
    "total_users": 10,
    "active_users": 8,
    "inactive_users": 1,
    "suspended_users": 1,
    "verified_users": 5
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 10
  }
}
```

### 创建用户

**POST** `/api/users`

创建新用户。

**需要认证：** 是  
**需要权限：** `users:create`

**请求体：** 与注册接口相同

### 获取用户详情

**GET** `/api/users/{id}`

获取指定用户的详细信息。

**需要认证：** 是  
**需要权限：** `users:read` 或者是用户本人

**响应：** 与用户列表中的单个用户格式相同

### 更新用户信息

**PUT** `/api/users/{id}`

更新用户信息。

**需要认证：** 是  
**需要权限：** `users:update` 或者是用户本人（限制字段）

**请求体：**
```json
{
  "username": "string",      // 可选
  "email": "string",         // 可选
  "first_name": "string",    // 可选
  "last_name": "string",     // 可选
  "status": "string",        // 可选，仅管理员可修改
  "email_verified": boolean  // 可选，仅管理员可修改
}
```

### 更新用户密码

**PUT** `/api/users/{id}/password`

更新用户密码。

**需要认证：** 是  
**需要权限：** `users:update` 或者是用户本人

**请求体：**
```json
{
  "currentPassword": "string",  // 用户本人修改时必需
  "newPassword": "string",      // 必需
  "confirmPassword": "string"   // 必需
}
```

### 删除用户

**DELETE** `/api/users/{id}`

删除用户账户。

**需要认证：** 是  
**需要权限：** `users:delete`

**注意：** 不能删除自己的账户

## 角色管理 API

### 获取角色列表

**GET** `/api/roles`

获取所有角色及其权限。

**需要认证：** 是  
**需要权限：** `users:read`

**响应：**
```json
{
  "success": true,
  "roles": [
    {
      "id": 1,
      "name": "admin",
      "display_name": "管理员",
      "description": "系统管理员，拥有所有权限",
      "is_system": true,
      "created_at": "2025-07-03T00:00:00.000Z",
      "permissions": [
        {
          "id": 1,
          "name": "users:create",
          "display_name": "创建用户",
          "resource": "users",
          "action": "create"
        }
      ]
    }
  ]
}
```

### 创建角色

**POST** `/api/roles`

创建新角色。

**需要认证：** 是  
**需要权限：** `users:manage`

**请求体：**
```json
{
  "name": "string",         // 必需，角色名称
  "display_name": "string", // 必需，显示名称
  "description": "string"   // 可选，描述
}
```

## 权限管理 API

### 获取权限列表

**GET** `/api/permissions`

获取所有权限，可按资源筛选。

**需要认证：** 是  
**需要权限：** `users:read`

**查询参数：**
- `resource`: 筛选指定资源的权限

**响应：**
```json
{
  "success": true,
  "permissions": [
    {
      "id": 1,
      "name": "users:create",
      "display_name": "创建用户",
      "description": "创建新用户账户",
      "resource": "users",
      "action": "create",
      "created_at": "2025-07-03T00:00:00.000Z"
    }
  ],
  "groupedPermissions": {
    "users": [...],
    "conversations": [...],
    "agents": [...]
  }
}
```

## 用户角色管理 API

### 获取用户角色

**GET** `/api/users/{id}/roles`

获取指定用户的所有角色。

**需要认证：** 是  
**需要权限：** `users:read`

### 分配角色给用户

**POST** `/api/users/{id}/roles`

为用户分配角色。

**需要认证：** 是  
**需要权限：** `users:update`

**请求体：**
```json
{
  "roleId": 1  // 角色ID
}
```

### 移除用户角色

**DELETE** `/api/users/{id}/roles`

移除用户的角色。

**需要认证：** 是  
**需要权限：** `users:update`

**请求体：**
```json
{
  "roleId": 1  // 角色ID
}
```

## 错误代码

- `400` - 请求参数错误
- `401` - 未认证或令牌无效
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突（如用户名已存在）
- `500` - 服务器内部错误

## 安全特性

1. **密码加密**：使用 bcrypt 加密存储密码
2. **JWT 令牌**：访问令牌1小时过期，刷新令牌7天过期
3. **输入验证**：使用 Zod 进行严格的输入验证
4. **权限控制**：基于角色的访问控制（RBAC）
5. **防护措施**：防止 SQL 注入、XSS 攻击等

## 使用示例

### JavaScript/TypeScript

```javascript
// 登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: '123456'
  })
});

const { accessToken } = await loginResponse.json();

// 获取用户列表
const usersResponse = await fetch('/api/users', {
  headers: { 'Authorization': `Bearer ${accessToken}` }
});

const { users } = await usersResponse.json();
```

### cURL

```bash
# 登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 获取用户列表
curl -X GET http://localhost:3000/api/users \
  -H "Authorization: Bearer <access_token>"
```
