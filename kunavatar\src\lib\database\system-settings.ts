import { db } from './connection';

export interface SystemSetting {
  id: number;
  key: string;
  value: string | null;
  description: string | null;
  category: string;
  created_at: string;
  updated_at: string;
}

export type CreateSystemSettingData = Omit<SystemSetting, 'id' | 'created_at' | 'updated_at'>;
export type UpdateSystemSettingData = Partial<Omit<SystemSetting, 'id' | 'created_at' | 'updated_at'>>;

// 系统设置相关查询语句
export const systemSettingQueries = {
  // 根据key获取设置
  getByKey: db.prepare(`
    SELECT * FROM system_settings
    WHERE key = ?
  `),

  // 根据分类获取设置
  getByCategory: db.prepare(`
    SELECT * FROM system_settings
    WHERE category = ?
    ORDER BY key
  `),

  // 获取所有设置
  getAll: db.prepare(`
    SELECT * FROM system_settings
    ORDER BY category, key
  `),

  // 创建新设置
  create: db.prepare(`
    INSERT INTO system_settings (key, value, description, category)
    VALUES (?, ?, ?, ?)
  `),

  // 更新设置值
  updateValue: db.prepare(`
    UPDATE system_settings
    SET value = ?, updated_at = CURRENT_TIMESTAMP
    WHERE key = ?
  `),

  // 更新设置（完整更新）
  update: db.prepare(`
    UPDATE system_settings
    SET value = ?, description = ?, category = ?, updated_at = CURRENT_TIMESTAMP
    WHERE key = ?
  `),

  // 删除设置
  delete: db.prepare(`
    DELETE FROM system_settings
    WHERE key = ?
  `),

  // 检查设置是否存在
  exists: db.prepare(`
    SELECT COUNT(*) as count FROM system_settings
    WHERE key = ?
  `)
};

// 系统设置操作类
export class SystemSettingOperations {
  /**
   * 根据key获取设置
   */
  static getByKey(key: string): SystemSetting | null {
    try {
      const result = systemSettingQueries.getByKey.get(key) as SystemSetting | undefined;
      return result || null;
    } catch (error) {
      console.error('获取系统设置失败:', error);
      return null;
    }
  }

  /**
   * 根据分类获取设置
   */
  static getByCategory(category: string): SystemSetting[] {
    try {
      return systemSettingQueries.getByCategory.all(category) as SystemSetting[];
    } catch (error) {
      console.error('获取分类设置失败:', error);
      return [];
    }
  }

  /**
   * 获取所有设置
   */
  static getAll(): SystemSetting[] {
    try {
      return systemSettingQueries.getAll.all() as SystemSetting[];
    } catch (error) {
      console.error('获取所有设置失败:', error);
      return [];
    }
  }

  /**
   * 创建新设置
   */
  static create(data: CreateSystemSettingData): SystemSetting | null {
    try {
      const result = systemSettingQueries.create.run(
        data.key,
        data.value,
        data.description,
        data.category
      );
      
      if (result.changes > 0) {
        return this.getByKey(data.key);
      }
      return null;
    } catch (error) {
      console.error('创建系统设置失败:', error);
      return null;
    }
  }

  /**
   * 更新设置值（仅更新value字段）
   */
  static updateValue(key: string, value: string): boolean {
    try {
      const result = systemSettingQueries.updateValue.run(value, key);
      return result.changes > 0;
    } catch (error) {
      console.error('更新设置值失败:', error);
      return false;
    }
  }

  /**
   * 更新设置（完整更新）
   */
  static update(key: string, data: UpdateSystemSettingData): boolean {
    try {
      const current = this.getByKey(key);
      if (!current) {
        return false;
      }

      const result = systemSettingQueries.update.run(
        data.value ?? current.value,
        data.description ?? current.description,
        data.category ?? current.category,
        key
      );
      return result.changes > 0;
    } catch (error) {
      console.error('更新系统设置失败:', error);
      return false;
    }
  }

  /**
   * 删除设置
   */
  static delete(key: string): boolean {
    try {
      const result = systemSettingQueries.delete.run(key);
      return result.changes > 0;
    } catch (error) {
      console.error('删除系统设置失败:', error);
      return false;
    }
  }

  /**
   * 检查设置是否存在
   */
  static exists(key: string): boolean {
    try {
      const result = systemSettingQueries.exists.get(key) as { count: number };
      return result.count > 0;
    } catch (error) {
      console.error('检查设置存在性失败:', error);
      return false;
    }
  }

  /**
   * 获取设置值（便捷方法）
   */
  static getValue(key: string, defaultValue: string = ''): string {
    const setting = this.getByKey(key);
    return setting?.value || defaultValue;
  }

  /**
   * 设置值（便捷方法）
   */
  static setValue(key: string, value: string): boolean {
    if (this.exists(key)) {
      return this.updateValue(key, value);
    } else {
      const created = this.create({
        key,
        value,
        description: null,
        category: 'general'
      });
      return created !== null;
    }
  }
}

// 导出便捷的操作函数
export const systemSettingOperations = SystemSettingOperations;