'use client';

import React from 'react';
import { useNotification } from '../NotificationContext';
import { Bell, Info, CheckCircle, AlertTriangle, AlertCircle } from 'lucide-react';

export function NotificationDemo() {
  const notification = useNotification();

  const showSuccess = () => {
    notification.success('操作成功', '您的操作已成功完成');
  };

  const showError = () => {
    notification.error('操作失败', '系统遇到了一个错误，请稍后重试');
  };

  const showWarning = () => {
    notification.warning('注意', '这是一个重要的警告信息');
  };

  const showInfo = () => {
    notification.info('提示', '这是一条信息提示');
  };

  const showCustomNotification = () => {
    notification.show({
      type: 'info',
      title: '自定义通知',
      message: '这是一个带有操作按钮的通知',
      duration: 0, // 不自动消失
      actions: [
        {
          label: '确认',
          variant: 'primary',
          onClick: () => {
            notification.success('已确认', '您已确认此操作');
          },
        },
        {
          label: '取消',
          variant: 'secondary',
          onClick: () => {
            notification.warning('已取消', '操作已取消');
          },
        },
      ],
    });
  };

  const showPersistentNotification = () => {
    notification.show({
      type: 'warning',
      title: '持久通知',
      message: '这个通知不会自动消失，需要手动关闭',
      duration: 0,
      dismissible: true,
    });
  };

  const showWithCustomIcon = () => {
    notification.show({
      type: 'info',
      title: '自定义图标',
      message: '这个通知使用了自定义图标',
      icon: <Bell className="w-5 h-5" />,
    });
  };

  const clearAllNotifications = () => {
    notification.dismissAll();
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-theme-foreground mb-2">
          通知系统演示
        </h2>
        <p className="text-theme-foreground-muted">
          点击下方按钮体验不同类型的通知效果
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 基础通知类型 */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-theme-foreground flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            基础类型
          </h3>
          
          <button
            onClick={showSuccess}
            className="w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
          >
            成功通知
          </button>
          
          <button
            onClick={showError}
            className="w-full px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
          >
            错误通知
          </button>
          
          <button
            onClick={showWarning}
            className="w-full px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
          >
            警告通知
          </button>
          
          <button
            onClick={showInfo}
            className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
          >
            信息通知
          </button>
        </div>

        {/* 高级功能 */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-theme-foreground flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            高级功能
          </h3>
          
          <button
            onClick={showCustomNotification}
            className="w-full px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
          >
            带操作按钮
          </button>
          
          <button
            onClick={showPersistentNotification}
            className="w-full px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
          >
            持久通知
          </button>
          
          <button
            onClick={showWithCustomIcon}
            className="w-full px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg transition-colors"
          >
            自定义图标
          </button>
          
          <button
            onClick={clearAllNotifications}
            className="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            清除所有通知
          </button>
        </div>
      </div>

      {/* 当前通知状态 */}
      <div className="mt-8 p-4 bg-theme-background-secondary rounded-lg border border-theme-border">
        <h4 className="text-sm font-medium text-theme-foreground mb-2">
          当前通知数量: {notification.notifications.length}
        </h4>
        {notification.notifications.length > 0 && (
          <div className="space-y-2">
            {notification.notifications.slice(0, 3).map((notif) => (
              <div key={notif.id} className="text-xs text-theme-foreground-muted">
                {notif.type}: {notif.title}
              </div>
            ))}
            {notification.notifications.length > 3 && (
              <div className="text-xs text-theme-foreground-muted">
                还有 {notification.notifications.length - 3} 条通知...
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 