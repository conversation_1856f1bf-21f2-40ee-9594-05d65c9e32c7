/**
 * 手动触发数据库迁移脚本
 */

import { db } from './src/lib/database/connection.ts';

console.log('触发数据库迁移...');
console.log('数据库连接已建立，迁移应该已经执行');

// 检查表结构
try {
  const conversationsTableInfo = db.prepare("PRAGMA table_info(conversations)").all();
  console.log('conversations表结构:', conversationsTableInfo);
  
  const messagesTableInfo = db.prepare("PRAGMA table_info(messages)").all();
  console.log('messages表结构:', messagesTableInfo);
  
  const hasUserIdInConversations = conversationsTableInfo.some(column => column.name === 'user_id');
  const hasUserIdInMessages = messagesTableInfo.some(column => column.name === 'user_id');
  
  console.log('conversations表是否有user_id字段:', hasUserIdInConversations);
  console.log('messages表是否有user_id字段:', hasUserIdInMessages);
  
  if (hasUserIdInConversations && hasUserIdInMessages) {
    console.log('✅ 数据库迁移成功完成');
  } else {
    console.log('❌ 数据库迁移未完成');
  }
} catch (error) {
  console.error('检查表结构失败:', error);
}

db.close();
