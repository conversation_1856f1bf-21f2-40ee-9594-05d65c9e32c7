@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入独立的markdown样式 */
@import '../styles/markdown.css';

/* 主题变量定义 - 浅色主题（默认） */
:root {
  /* 主题变量系统 */
  --color-background: #f7f7f7;
  --color-background-secondary: #f7f7f7;
  --color-background-tertiary: #f0f0f0;

  --color-foreground: #111111;
  --color-foreground-secondary: #555555;
  --color-foreground-muted: #777777;

  --color-border: #e5e5e5;
  --color-border-secondary: #dcdcdc;

  --color-card: #ffffff;
  --color-card-hover: #f7f7f7;

  --color-input: #ffffff;
  --color-input-border: #cccccc;
  --color-input-focus: #3b82f6;

  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
  --color-secondary: #6b7280;
  --color-secondary-hover: #4b5563;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #4930D9;
  --color-accent-hover: #3f26cf;

  /* 字体大小系统 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */

  /* 行高系统 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* 标题层级系统 */
  --heading-h1-size: var(--font-size-4xl);
  --heading-h1-weight: 700;
  --heading-h1-line-height: var(--line-height-tight);

  --heading-h2-size: var(--font-size-3xl);
  --heading-h2-weight: 600;
  --heading-h2-line-height: var(--line-height-tight);

  --heading-h3-size: var(--font-size-2xl);
  --heading-h3-weight: 600;
  --heading-h3-line-height: var(--line-height-snug);

  --heading-h4-size: var(--font-size-xl);
  --heading-h4-weight: 600;
  --heading-h4-line-height: var(--line-height-snug);

  --heading-h5-size: var(--font-size-lg);
  --heading-h5-weight: 500;
  --heading-h5-line-height: var(--line-height-normal);

  --heading-h6-size: var(--font-size-base);
  --heading-h6-weight: 500;
  --heading-h6-line-height: var(--line-height-normal);

  /* 页面标题系统 */
  --page-title-size: var(--font-size-3xl);
  --page-title-weight: 700;
  --page-title-line-height: var(--line-height-tight);

  --page-subtitle-size: var(--font-size-base);
  --page-subtitle-weight: 400;
  --page-subtitle-line-height: var(--line-height-normal);

  --section-title-size: var(--font-size-xl);
  --section-title-weight: 600;
  --section-title-line-height: var(--line-height-snug);

  --card-title-size: var(--font-size-xl);
  --card-title-weight: 600;
  --card-title-line-height: var(--line-height-snug);

  /* 间距系统 */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  --spacing-3xl: 4rem;      /* 64px */

  /* 圆角系统 */
  --radius-sm: 0.25rem;     /* 4px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */
  --radius-2xl: 1.5rem;     /* 24px */

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 传统变量（兼容现有代码） */
  --background: var(--color-background);
  --foreground: var(--color-foreground);
}

/* 深色主题变量 */
.dark {
  /* 主题变量系统 - 深色主题 */
  --color-background: #121212;
  --color-background-secondary: #1e1e1e;
  --color-background-tertiary: #282828;

  --color-foreground: #f5f5f5;
  --color-foreground-secondary: #b3b3b3;
  --color-foreground-muted: #808080;

  --color-border: #333333;
  --color-border-secondary: #444444;

  --color-card: #1e1e1e;
  --color-card-hover: #282828;

  --color-input: #1e1e1e;
  --color-input-border: #444444;
  --color-input-focus: #6854da;

  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
  --color-secondary: #6b7280;
  --color-secondary-hover: #9ca3af;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-accent: #4930D9;
  --color-accent-hover: #3f26cf;

  /* 深色主题下的阴影调整 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);

  /* 传统变量（兼容现有代码） */
  --background: var(--color-background);
  --foreground: var(--color-foreground);
}

/* 主题色定义 */
/* 默认主题: Kun */
:root,
.dark,
html[data-color-theme="kun"] {
  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
}

html[data-color-theme="green"] {
  --color-primary: #4D564F;
  --color-primary-rgb: 79, 186, 174;
  --color-primary-hover: #3A413B;
}

html[data-color-theme="purple"] {
  --color-primary: #6a5ac2;
  --color-primary-rgb: 106, 90, 194;
  --color-primary-hover: #5a4aae;
}

html[data-color-theme="orange"] {
  --color-primary: #E07800;
  --color-primary-rgb: 224, 120, 0;
  --color-primary-hover: #CC6D00;
}

html[data-color-theme="blue"] {
  --color-primary: #284B7B;
  --color-primary-rgb: 30, 56, 92;
  --color-primary-hover: #1E385C;
}

html[data-color-theme="raspberry"] {
  --color-primary: #EC4680;
  --color-primary-rgb: 236, 70, 128;
  --color-primary-hover: #E82167;
}

html[data-color-theme="moonstone"] {
  --color-primary: #61A0AF;
  --color-primary-rgb: 97, 160, 175;
  --color-primary-hover: #5293A3;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 全局标题样式类 */
.heading-h1 {
  font-size: var(--heading-h1-size);
  font-weight: var(--heading-h1-weight);
  line-height: var(--heading-h1-line-height);
  color: var(--color-foreground);
}

.heading-h2 {
  font-size: var(--heading-h2-size);
  font-weight: var(--heading-h2-weight);
  line-height: var(--heading-h2-line-height);
  color: var(--color-foreground);
}

.heading-h3 {
  font-size: var(--heading-h3-size);
  font-weight: var(--heading-h3-weight);
  line-height: var(--heading-h3-line-height);
  color: var(--color-foreground);
}

.heading-h4 {
  font-size: var(--heading-h4-size);
  font-weight: var(--heading-h4-weight);
  line-height: var(--heading-h4-line-height);
  color: var(--color-foreground);
}

.heading-h5 {
  font-size: var(--heading-h5-size);
  font-weight: var(--heading-h5-weight);
  line-height: var(--heading-h5-line-height);
  color: var(--color-foreground);
}

.heading-h6 {
  font-size: var(--heading-h6-size);
  font-weight: var(--heading-h6-weight);
  line-height: var(--heading-h6-line-height);
  color: var(--color-foreground);
}

/* 页面专用标题样式类 */
.page-title {
  font-size: var(--page-title-size);
  font-weight: var(--page-title-weight);
  line-height: var(--page-title-line-height);
  color: var(--color-foreground);
}

.page-subtitle {
  font-size: var(--page-subtitle-size);
  font-weight: var(--page-subtitle-weight);
  line-height: var(--page-subtitle-line-height);
  color: var(--color-foreground-muted);
}

.section-title {
  font-size: var(--section-title-size);
  font-weight: var(--section-title-weight);
  line-height: var(--section-title-line-height);
  color: var(--color-foreground);
}

.card-title {
  font-size: var(--card-title-size);
  font-weight: var(--card-title-weight);
  line-height: var(--card-title-line-height);
  color: var(--color-foreground);
}

/* 字体样式类 */
.font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

.font-sans {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* 字重工具类 */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 防止闪烁的预加载样式 */
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换时的平滑过渡 */
html.theme-changing * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* 自定义滚动条样式 - 支持主题 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.8);
}

/* 聊天容器响应式宽度控制 */
.chat-container-responsive {
  width: 100% !important;
  max-width: 100% !important;
}

/* 全屏模式：始终占满全屏宽度 */
.chat-container-responsive.fullscreen {
  width: 100% !important;
  max-width: 100% !important;
}

/* 紧凑模式：1920px以下全屏，1920px以上80%宽度 */
.chat-container-responsive.compact {
  width: 100% !important;
  max-width: 100% !important;
}

@media (min-width: 1440px) {
  .chat-container-responsive.compact {
    width: 50% !important;
    max-width: 50% !important;
  }
}

/* 3D立方体加载动画样式 */
.spinner {
  width: 16px;
  height: 16px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

/* 小尺寸3D立方体加载动画样式 */
.spinner-small {
  width: 18px;
  height: 18px;
  animation: spinner-y0fdc1 2s infinite ease;
  transform-style: preserve-3d;
}

.spinner > div,
.spinner-small > div {
  background-color: rgba(59, 130, 246, 0.1);
  height: 100%;
  position: absolute;
  width: 100%;
  border: 1px solid var(--color-primary);
}

.spinner div:nth-of-type(1) {
  transform: translateZ(-20px) rotateY(180deg);
}

.spinner div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner div:nth-of-type(6) {
  transform: translateZ(20px);
}

/* 小尺寸立方体的变换样式 */
.spinner-small div:nth-of-type(1) {
  transform: translateZ(-12px) rotateY(180deg);
}

.spinner-small div:nth-of-type(2) {
  transform: rotateY(-270deg) translateX(50%);
  transform-origin: top right;
}

.spinner-small div:nth-of-type(3) {
  transform: rotateY(270deg) translateX(-50%);
  transform-origin: center left;
}

.spinner-small div:nth-of-type(4) {
  transform: rotateX(90deg) translateY(-50%);
  transform-origin: top center;
}

.spinner-small div:nth-of-type(5) {
  transform: rotateX(-90deg) translateY(50%);
  transform-origin: bottom center;
}

.spinner-small div:nth-of-type(6) {
  transform: translateZ(-12px);
}

@keyframes spinner-y0fdc1 {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotateY(90deg);
  }
  50% {
    transform: rotateY(90deg) rotateZ(90deg);
  }
  75% {
    transform: rotateY(180deg) rotateZ(90deg);
  }
  100% {
    transform: rotateY(180deg) rotateZ(180deg);
  }
}

/* 侧边栏相关样式 */
html[data-sidebar-state="collapsed"] .sidebar-container {
  width: 64px;
}

html[data-sidebar-state="expanded"] .sidebar-container {
  width: 208px;
}

.sidebar-container {
  transition: width 0.3s ease;
}

/* 防止hydration闪烁 - 最高优先级规则，立即生效 */
html[data-sidebar-state="collapsed"] .sidebar-text {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  overflow: hidden !important;
}

html[data-sidebar-state="expanded"] .sidebar-text {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: auto !important;
  overflow: visible !important;
}

/* 收缩状态下显示的元素 */
html[data-sidebar-state="expanded"] .sidebar-collapsed-only {
  display: none !important;
}

html[data-sidebar-state="collapsed"] .sidebar-collapsed-only {
  display: flex !important;
}

/* 按钮布局控制 */
html[data-sidebar-state="expanded"] .sidebar-button {
  justify-content: flex-start !important;
  padding: 0.75rem 1rem 0.75rem 0.75rem !important;
}

html[data-sidebar-state="collapsed"] .sidebar-button {
  justify-content: center !important;
  padding: 0.75rem !important;
}

/* 图标容器样式 */
html[data-sidebar-state="collapsed"] .sidebar-icon-container {
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  margin: 0 auto !important;
}

html[data-sidebar-state="expanded"] .sidebar-icon-container {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  flex-shrink: 0 !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  margin: 0 !important;
}

/* 工具提示显示控制 */
html[data-sidebar-state="collapsed"] .sidebar-tooltip {
  display: flex !important;
}

html[data-sidebar-state="expanded"] .sidebar-tooltip {
  display: none !important;
}

/* 导航栏选中状态的右侧竖条样式 */
.sidebar-nav-item {
  position: relative;
}

.sidebar-nav-item.active {
  background-color: transparent !important;
}

.sidebar-nav-item.active::after {
  content: '';
  position: absolute;
  right: -1px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 32px;
  background: linear-gradient(135deg, 
    var(--color-primary) 0%, 
    var(--color-primary-hover) 100%);
  border-radius: 3px 0 0 3px;
  box-shadow: 0 2px 6px rgba(var(--color-primary-rgb), 0.25);
  transition: all 0.2s ease-in-out;
}

/* 收缩状态下的选中竖条调整 */
html[data-sidebar-state="collapsed"] .sidebar-nav-item.active::after {
  right: 0;
  height: 28px;
  width: 4px;
}

/* 悬停状态保持原有效果 */
.sidebar-nav-item:hover {
  background-color: var(--theme-card-hover) !important;
}

.sidebar-nav-item.active:hover {
  background-color: var(--theme-card-hover) !important;
}

.sidebar-nav-item.active:hover::after {
  height: 36px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

html[data-sidebar-state="collapsed"] .sidebar-nav-item.active:hover::after {
  height: 32px;
}

/* 收缩状态下主题切换区域的特殊处理 */
html[data-sidebar-state="collapsed"] .sidebar-button:has(.bg-gradient-to-br) {
  justify-content: center !important;
  padding: 0.75rem !important;
}

html[data-sidebar-state="collapsed"] .sidebar-button:has(.bg-gradient-to-br) .sidebar-icon-container {
  margin: 0 !important;
}

/* 主题切换图标容器特殊处理 */
html[data-sidebar-state="collapsed"] .theme-toggle-icon {
  margin: 0 auto !important;
  width: auto !important;
  height: auto !important;
}

/* 统一图标容器过渡效果，防止抖动 */
.sidebar-icon-container {
  transition: width 0.3s ease, height 0.3s ease, margin 0.3s ease !important;
}

/* 确保图标本身不会抖动 */
.sidebar-icon-container > * {
  transition: none !important;
}

/* 抽屉弹窗滑动动画 */
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 菜单缩放动画 */
@keyframes scale-up {
  from {
    transform: scale(0.95) translateY(10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.animate-scale-up {
  animation: scale-up 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

@layer base {
  .fade-in-char {
    position: relative;
    opacity: 0;
    animation: fadeInChar 0.1s ease-out forwards;
  }
  
  @keyframes fadeInChar {
    to {
      opacity: 1;
    }
  }
}

/* Markdown 全局样式补充 - 与独立样式文件配合使用 */
.markdown-renderer .hljs {
  background: transparent !important;
}

/* 表单输入框优化 */
.form-input-base {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  background-color: var(--color-background-secondary);
  color: var(--color-foreground);
  transition: all 0.2s ease-in-out;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.form-input-base:hover {
  border-color: var(--color-border-secondary);
}

.form-input-base:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  background-color: var(--color-background);
}

.form-input-base::placeholder {
  color: var(--color-foreground-muted);
}

/* 错误状态 */
.form-input-base.error {
  border-color: var(--color-error);
  background-color: rgba(var(--color-error), 0.05);
}

/* 按钮基础样式 */
.btn-base {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background-color: var(--color-background-secondary);
  color: var(--color-foreground);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-card-hover);
  border-color: var(--color-border-secondary);
}

/* 标签样式 */
.tag-base {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.tag-primary {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
}

/* Steam风格卡片样式 */
.steam-card {
  position: relative;
  background: linear-gradient(145deg, var(--color-card), var(--color-background-secondary));
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    var(--color-primary) 0%, 
    var(--color-accent) 100%);
  opacity: 0.8;
}

.steam-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(var(--color-primary-rgb), 0.3);
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.steam-card-glow {
  position: absolute;
  inset: 0;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border: 1px solid rgba(var(--color-primary-rgb), 0.3);
  box-shadow: 
    0 0 20px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-card:hover .steam-card-glow {
  opacity: 1;
}

/* Steam风格统计卡片 */
.steam-stat-card {
  background: var(--color-background-tertiary);
  border: 1px solid var(--color-border-secondary);
  border-radius: 0.75rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.steam-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(var(--color-primary-rgb), 0.3) 50%, 
    transparent 100%);
}

.steam-stat-card:hover {
  background: var(--color-card);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-1px);
}

/* Steam风格标签 */
.steam-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.steam-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 100%);
  transition: left 0.5s ease;
}

.steam-tag:hover::before {
  left: 100%;
}

.steam-tag-success {
  background-color: rgba(var(--color-success), 0.1);
  color: var(--color-success);
  border-color: rgba(var(--color-success), 0.2);
}

.steam-tag-warning {
  background-color: rgba(var(--color-warning), 0.1);
  color: var(--color-warning);
  border-color: rgba(var(--color-warning), 0.2);
}

.steam-tag-info {
  background-color: rgba(var(--color-info), 0.1);
  color: var(--color-info);
  border-color: rgba(var(--color-info), 0.2);
}

/* Steam风格按钮 */
.steam-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  color: var(--color-foreground-muted);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.steam-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.steam-button:hover {
  transform: scale(1.1);
}

.steam-button:hover::before {
  opacity: 0.1;
}

.steam-button-info:hover {
  color: var(--color-info);
}

.steam-button-warning:hover {
  color: var(--color-warning);
}

.steam-button-error:hover {
  color: var(--color-error);
}

/* Steam风格图标容器 */
.steam-icon-container {
  position: relative;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  box-shadow: 
    0 8px 16px rgba(var(--color-primary-rgb), 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.steam-icon-container:hover {
  box-shadow: 
    0 12px 24px rgba(var(--color-primary-rgb), 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.steam-icon-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  background: var(--color-success);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid var(--color-card);
}

/* Steam风格空状态 */
.steam-empty-state {
  background: linear-gradient(135deg, 
    var(--color-card) 0%, 
    var(--color-background-secondary) 100%);
  border: 1px solid var(--color-border);
  border-radius: 1.5rem;
  backdrop-filter: blur(8px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.steam-empty-icon {
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.1) 0%, 
    rgba(var(--color-primary-rgb), 0.05) 100%);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  border-radius: 1.5rem;
  box-shadow: 
    0 4px 12px rgba(var(--color-primary-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 响应式网格增强 */
.steam-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .steam-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .steam-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1536px) {
  .steam-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 自定义工具类 */
.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}



