/**
 * 简化的数据隔离测试脚本
 * 测试用户只能访问自己的对话和消息数据
 */

import { dbOperations } from './src/lib/database/index.ts';
import crypto from 'crypto';

function runDataIsolationTest() {
  console.log('=== 开始数据隔离测试 ===');
  
  try {
    // 创建测试用户ID
    const user1Id = crypto.randomUUID();
    const user2Id = crypto.randomUUID();
    
    console.log('测试用户ID:');
    console.log('用户1 ID:', user1Id);
    console.log('用户2 ID:', user2Id);
    
    // 创建测试对话
    console.log('\n创建测试对话:');
    
    const conv1Id = dbOperations.createConversation({
      title: '用户1的对话1',
      model: 'llama3.2',
      user_id: user1Id
    });
    console.log('用户1对话1 ID:', conv1Id);
    
    const conv2Id = dbOperations.createConversation({
      title: '用户2的对话1',
      model: 'llama3.2',
      user_id: user2Id
    });
    console.log('用户2对话1 ID:', conv2Id);
    
    // 创建测试消息
    console.log('\n创建测试消息:');
    
    const msg1Id = dbOperations.createMessage({
      conversation_id: conv1Id,
      role: 'user',
      content: '用户1在对话1中的消息',
      model: 'llama3.2',
      user_id: user1Id
    });
    console.log('用户1对话1消息 ID:', msg1Id);
    
    const msg2Id = dbOperations.createMessage({
      conversation_id: conv2Id,
      role: 'user',
      content: '用户2在对话1中的消息',
      model: 'llama3.2',
      user_id: user2Id
    });
    console.log('用户2对话1消息 ID:', msg2Id);
    
    // 测试数据隔离
    console.log('\n=== 测试数据隔离 ===');
    
    // 测试1: 用户1只能看到自己的对话
    console.log('\n测试1: 用户对话隔离');
    const user1Conversations = dbOperations.getAllConversationsByUserId(user1Id);
    const user2Conversations = dbOperations.getAllConversationsByUserId(user2Id);
    
    console.log('用户1的对话数量:', user1Conversations.length);
    console.log('用户2的对话数量:', user2Conversations.length);
    
    if (user1Conversations.length === 1 && user2Conversations.length === 1) {
      console.log('✅ 对话隔离测试通过');
    } else {
      console.log('❌ 对话隔离测试失败');
    }
    
    // 测试2: 用户1不能访问用户2的对话
    console.log('\n测试2: 跨用户对话访问控制');
    const user1AccessToUser2Conv = dbOperations.getConversationByIdAndUserId(conv2Id, user1Id);
    const user2AccessToUser1Conv = dbOperations.getConversationByIdAndUserId(conv1Id, user2Id);
    
    if (!user1AccessToUser2Conv && !user2AccessToUser1Conv) {
      console.log('✅ 跨用户对话访问控制测试通过');
    } else {
      console.log('❌ 跨用户对话访问控制测试失败');
    }
    
    // 测试3: 用户只能看到自己的消息
    console.log('\n测试3: 消息隔离');
    const user1Messages = dbOperations.getMessagesByConversationIdAndUserId(conv1Id, user1Id);
    const user2Messages = dbOperations.getMessagesByConversationIdAndUserId(conv2Id, user2Id);
    const user1AccessToUser2Messages = dbOperations.getMessagesByConversationIdAndUserId(conv2Id, user1Id);
    
    console.log('用户1在自己对话中的消息数量:', user1Messages.length);
    console.log('用户2在自己对话中的消息数量:', user2Messages.length);
    console.log('用户1尝试访问用户2对话的消息数量:', user1AccessToUser2Messages.length);
    
    if (user1Messages.length === 1 && user2Messages.length === 1 && user1AccessToUser2Messages.length === 0) {
      console.log('✅ 消息隔离测试通过');
    } else {
      console.log('❌ 消息隔离测试失败');
    }
    
    // 测试4: 对话删除权限控制
    console.log('\n测试4: 对话删除权限控制');
    const deleteResult1 = dbOperations.deleteConversationByUserAndId(conv2Id, user1Id); // 用户1尝试删除用户2的对话
    const deleteResult2 = dbOperations.deleteConversationByUserAndId(conv1Id, user1Id); // 用户1删除自己的对话
    
    if (!deleteResult1 && deleteResult2) {
      console.log('✅ 对话删除权限控制测试通过');
    } else {
      console.log('❌ 对话删除权限控制测试失败');
    }
    
    // 清理测试数据
    console.log('\n清理测试数据...');
    try {
      dbOperations.deleteConversation(conv2Id); // 删除剩余的对话
      console.log('✅ 测试数据清理完成');
    } catch (error) {
      console.log('❌ 清理测试数据失败:', error.message);
    }
    
    console.log('\n=== 数据隔离测试完成 ===');
    console.log('🎉 所有测试已完成！请检查上面的测试结果。');
    
  } catch (error) {
    console.error('测试执行失败:', error);
  }
}

// 运行测试
runDataIsolationTest();

export { runDataIsolationTest };
