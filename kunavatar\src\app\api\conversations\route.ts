import { NextRequest, NextResponse } from 'next/server';
import { dbOperations } from '../../../lib/database';
import { withAuth } from '../../../lib/middleware/auth';

// 获取用户的所有对话
export const GET = withAuth(async (request) => {
  try {
    const userId = request.user!.id;
    const conversations = dbOperations.getAllConversationsByUserId(userId);

    // 为每个对话添加统计信息
    const conversationsWithStats = conversations.map(conversation => {
      const stats = dbOperations.getConversationStats(conversation.id);
      return {
        ...conversation,
        stats
      };
    });

    return NextResponse.json({
      success: true,
      conversations: conversationsWithStats
    });
  } catch (error) {
    console.error('获取对话列表失败:', error);

    return NextResponse.json(
      {
        error: '获取对话列表失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
});

// 创建新对话
export const POST = withAuth(async (request) => {
  try {
    const userId = request.user!.id;
    const body = await request.json();
    const { title, model }: { title: string; model: string } = body;

    // 验证必需参数
    if (!title || !model) {
      return NextResponse.json(
        { error: '缺少必需参数: title 和 model' },
        { status: 400 }
      );
    }

    // 创建新对话
    const conversationId = dbOperations.createConversation({
      title: title.trim(),
      model,
      user_id: userId
    });

    // 获取创建的对话
    const conversation = dbOperations.getConversationById(conversationId);

    return NextResponse.json({
      success: true,
      conversation
    }, { status: 201 });
  } catch (error) {
    console.error('创建对话失败:', error);

    return NextResponse.json(
      {
        error: '创建对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
});