'use client';

import React, { useState, useRef, useEffect } from 'react';
import { AgentWithRelations } from '@/app/agents/types';
import { Bot, X } from 'lucide-react';

interface AgentSelectorProps {
  agents: AgentWithRelations[];
  selectedAgentId: number | null;
  onAgentChange: (agentId: number | null) => void;
  disabled?: boolean;
  className?: string;
}

export function AgentSelector({
  agents,
  selectedAgentId,
  onAgentChange,
  disabled = false,
  className = '',
}: AgentSelectorProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const currentAgent = selectedAgentId ? agents.find(a => a.id === selectedAgentId) : null;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
        className={`form-input-base flex items-center gap-3 text-left ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
        disabled={disabled}
      >
        {currentAgent ? (
          currentAgent.avatar ? (
            <img 
              src={currentAgent.avatar} 
              alt={currentAgent.name}
              className="w-5 h-5 rounded-md object-cover flex-shrink-0"
            />
          ) : (
            <div className="w-5 h-5 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center flex-shrink-0">
              <Bot className="w-3 h-3 text-white" />
            </div>
          )
        ) : (
          <div className="w-5 h-5 bg-theme-background-secondary rounded-md border border-theme-border flex-shrink-0 flex items-center justify-center">
            <Bot className="w-4 h-4 text-theme-foreground-muted" />
          </div>
        )}
        
        <span className="flex-1 text-left truncate">
          {currentAgent?.name || '请选择Agent'}
        </span>
        
        <svg
          className={`w-4 h-4 text-theme-foreground-muted transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-theme-card border border-theme-border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto scrollbar-thin">
          {/* 无Agent选项 */}
          <button
            onClick={() => {
              onAgentChange(null);
              setIsDropdownOpen(false);
            }}
            className={`w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-theme-card-hover transition-colors duration-200 ${
              !selectedAgentId ? 'bg-theme-background-secondary' : ''
            }`}
          >
            <div className="w-5 h-5 bg-gray-400 rounded-md flex items-center justify-center flex-shrink-0">
              <X className="w-3 h-3 text-white" />
            </div>
            
            <span className="flex-1 truncate text-theme-foreground-muted">
              无Agent
            </span>
            
            {!selectedAgentId && (
              <svg className="w-4 h-4 text-theme-primary" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </button>
          
          {/* Agent列表 */}
          {agents.length > 0 ? agents.map((agent) => {
            const isSelected = agent.id === selectedAgentId;
            
            return (
              <button
                key={agent.id}
                onClick={() => {
                  onAgentChange(agent.id);
                  setIsDropdownOpen(false);
                }}
                className={`w-full flex items-center gap-3 px-3 py-2 text-sm text-left hover:bg-theme-card-hover transition-colors duration-200 ${
                  isSelected ? 'bg-theme-background-secondary' : ''
                }`}
              >
                {agent.avatar ? (
                  <img 
                    src={agent.avatar} 
                    alt={agent.name}
                    className="w-5 h-5 rounded-md object-cover flex-shrink-0"
                  />
                ) : (
                  <div className="w-5 h-5 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center flex-shrink-0">
                    <Bot className="w-3 h-3 text-white" />
                  </div>
                )}
                
                <span className="flex-1 truncate text-theme-foreground">
                  {agent.name}
                </span>
                
                {isSelected && (
                  <svg className="w-4 h-4 text-theme-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            );
          }) : (
            <div className="px-3 py-2 text-sm text-theme-foreground-muted">没有可用Agent</div>
          )}
        </div>
      )}
    </div>
  );
}