'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Loading from '@/components/Loading';

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // 自动重定向到聊天页面
    router.replace('/simple-chat?new=true');
  }, [router]);

  return (
    <div className="flex h-screen bg-theme-background items-center justify-center">
      <Loading 
        size="normal"
        text="Kun Avatar正在初始化，请稍等"
        showText={true}
        containerStyle={{
          padding: '3rem'
        }}
      />
    </div>
  );
}