'use client';

import React, { useState, useEffect, useRef } from 'react';

const THEME_STORAGE_KEY = 'color-theme';

const themes = [
  { name: 'kun', color: '#6a5ac2', displayName: '默认' },
  { name: 'green', color: '#4D564F', displayName: '芦苇绿' },
  { name: 'purple', color: '#6a5ac2', displayName: '优雅紫' },
  { name: 'orange', color: '#E07800', displayName: '活力橙' },
  { name: 'blue', color: '#284B7B', displayName: '伯克利蓝' },
  { name: 'raspberry', color: '#EC4680', displayName: '覆盆子' },
  { name: 'moonstone', color: '#61A0AF', displayName: '月长石' },
];

export function ColorThemeSwitcher() {
  const [activeTheme, setActiveTheme] = useState('kun');
  const [isPaletteOpen, setPaletteOpen] = useState(false);
  const paletteRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) || 'kun';
    setActiveTheme(savedTheme);
    document.documentElement.setAttribute('data-color-theme', savedTheme);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (paletteRef.current && !paletteRef.current.contains(event.target as Node)) {
        setPaletteOpen(false);
      }
    };

    if (isPaletteOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPaletteOpen]);

  const changeTheme = (themeName: string) => {
    document.documentElement.setAttribute('data-color-theme', themeName);
    localStorage.setItem(THEME_STORAGE_KEY, themeName);
    setActiveTheme(themeName);
    setPaletteOpen(false);
  };

  const activeColor = themes.find(t => t.name === activeTheme)?.color || '#F36D68';

  return (
    <div className="relative" ref={paletteRef}>
      <button
        onClick={() => setPaletteOpen(!isPaletteOpen)}
        className="sidebar-expanded-only w-5 h-5 rounded-full bg-gradient-to-br from-theme-primary to-theme-primary-hover flex-shrink-0 transition-all duration-300 transform hover:scale-110"
        style={{
          backgroundColor: activeColor,
          backgroundImage: `linear-gradient(135deg, ${activeColor} 0%, var(--color-primary-hover) 100%)`
        }}
        aria-label="选择主题颜色"
      />
      {isPaletteOpen && (
        <div 
          className="absolute bottom-full mb-3 left-1/2 -translate-x-1/2 z-20"
        >
          <div className="flex flex-col items-center space-y-3 p-3 bg-theme-card/50 backdrop-blur-sm rounded-lg">
            {themes.map((theme) => (
              <button
                key={theme.name}
                title={theme.displayName}
                onClick={() => changeTheme(theme.name)}
                className="relative group w-6 h-6 rounded-full transition-all duration-200 transform hover:scale-125 focus:outline-none"
                style={{ 
                  backgroundColor: theme.color,
                  boxShadow: activeTheme === theme.name 
                    ? `0 0 0 2px var(--color-background), 0 0 0 4px ${theme.color}` 
                    : '0 0 0 1px rgba(0,0,0,0.1)'
                }}
              >
                <span className="absolute top-1/2 -translate-y-1/2 left-full ml-3 px-2 py-1 rounded-md text-xs bg-gray-900/80 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
                  {theme.displayName}
                </span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 