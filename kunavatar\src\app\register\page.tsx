'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Eye, EyeOff, UserPlus } from 'lucide-react';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // 验证密码确认
    if (formData.password !== formData.confirmPassword) {
      setError('密码确认不匹配');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setSuccess(true);
        setTimeout(() => {
          router.push('/login');
        }, 2000);
      } else {
        if (data.details) {
          // 显示详细的验证错误
          const errorMessages = data.details.map((detail: any) => detail.message).join(', ');
          setError(errorMessages);
        } else {
          setError(data.error || '注册失败');
        }
      }
    } catch (error) {
      console.error('注册失败:', error);
      setError('注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // 对用户名、邮箱和密码字段去除空格
    const trimmedValue = (name === 'username' || name === 'email' || name === 'password' || name === 'confirmPassword') 
      ? value.trim() 
      : value;
    
    setFormData(prev => ({
      ...prev,
      [name]: trimmedValue,
    }));
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-theme-background-secondary py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
              <UserPlus className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-theme-foreground">
              注册成功！
            </h2>
            <p className="mt-2 text-center text-sm text-theme-foreground-muted">
              您的账户已创建成功，正在跳转到登录页面...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-theme-background-secondary py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-theme-primary/10">
            <UserPlus className="h-6 w-6 text-theme-primary" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-theme-foreground">
            创建新账户
          </h2>
          <p className="mt-2 text-center text-sm text-theme-foreground-muted">
            或者{' '}
            <Link href="/login" className="font-medium text-theme-primary hover:text-theme-primary-hover">
              登录到现有账户
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium text-theme-foreground">
                  名字
                </label>
                <input
                  id="first_name"
                  name="first_name"
                  type="text"
                  value={formData.first_name}
                  onChange={handleChange}
                  className="mt-1 appearance-none relative block w-full px-3 py-2 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                  placeholder="名字"
                />
              </div>
              <div>
                <label htmlFor="last_name" className="block text-sm font-medium text-theme-foreground">
                  姓氏
                </label>
                <input
                  id="last_name"
                  name="last_name"
                  type="text"
                  value={formData.last_name}
                  onChange={handleChange}
                  className="mt-1 appearance-none relative block w-full px-3 py-2 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                  placeholder="姓氏"
                />
              </div>
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-theme-foreground">
                用户名 *
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                value={formData.username}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-theme-foreground">
                邮箱 *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                placeholder="请输入邮箱地址"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-theme-foreground">
                密码 *
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                  placeholder="请输入密码（至少6位）"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-theme-foreground-muted" />
                  ) : (
                    <Eye className="h-4 w-4 text-theme-foreground-muted" />
                  )}
                </button>
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-theme-foreground">
                确认密码 *
              </label>
              <div className="mt-1 relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                  placeholder="请再次输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-theme-foreground-muted" />
                  ) : (
                    <Eye className="h-4 w-4 text-theme-foreground-muted" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-theme-primary hover:bg-theme-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? '注册中...' : '创建账户'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
