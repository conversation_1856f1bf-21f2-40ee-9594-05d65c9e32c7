export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  tool_calls?: any[];
  tool_call_id?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolCallResult {
  tool_call_id: string;
  content: string;
}

export type AIStatus = 'idle' | 'loading' | 'generating' | 'tool_calling' | 'thinking';

export interface AIState {
  status: AIStatus;
  message?: string;
  toolName?: string;
  progress?: number;
  thinkingStartTime?: number;
}

export interface SimpleMessage {
  id: string;
  role: 'user' | 'assistant' | 'tool_call' | 'system' | 'tool' | 'tool_result';
  content: string;
  timestamp: number;
  model?: string;
  toolCall?: ToolCallRuntime;
  // 统计字段（可选）
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface ToolCallRuntime {
  id: string;
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}