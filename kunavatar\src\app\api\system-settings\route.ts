import { NextRequest, NextResponse } from 'next/server';
import { systemSettingOperations } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');
    const category = searchParams.get('category');

    if (key) {
      // 获取单个设置
      const setting = systemSettingOperations.getByKey(key);
      if (!setting) {
        return NextResponse.json([]); // 如果找不到，返回空数组以保持一致性
      }
      return NextResponse.json([setting]); // 以数组形式返回单个设置
    } else if (category) {
      // 获取分类设置
      const settings = systemSettingOperations.getByCategory(category);
      return NextResponse.json(settings); // 直接返回数组
    } else {
      // 获取所有设置
      const settings = systemSettingOperations.getAll();
      return NextResponse.json(settings); // 直接返回数组
    }
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { key, value, description, category } = await request.json();

    if (!key) {
      return NextResponse.json(
        { success: false, error: '设置键不能为空' },
        { status: 400 }
      );
    }

    // 检查设置是否已存在
    const exists = systemSettingOperations.exists(key);
    
    if (exists) {
      return NextResponse.json(
        { success: false, error: '设置已存在，请使用PUT方法更新' },
        { status: 409 }
      );
    }

    const newSetting = systemSettingOperations.create({
      key,
      value: value || null,
      description: description || null,
      category: category || 'general'
    });

    if (!newSetting) {
      return NextResponse.json(
        { success: false, error: '创建设置失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data: newSetting });
  } catch (error) {
    console.error('创建系统设置失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { key, value } = await request.json();

    if (!key || value === undefined) {
      return NextResponse.json(
        { success: false, error: '设置键和值不能为空' },
        { status: 400 }
      );
    }

    const exists = systemSettingOperations.exists(key);
    let success;

    if (exists) {
      // 如果存在，则更新值
      success = systemSettingOperations.updateValue(key, String(value));
    } else {
      // 如果不存在，则创建新设置
      // 因为前端只发送key和value，我们为新设置提供默认值
      // 对于记忆设置，category应为 'memory'
      const category = key.startsWith('memory_') ? 'memory' : 'general';
      const newSetting = systemSettingOperations.create({
        key,
        value: String(value),
        description: `自动创建的设置： ${key}`,
        category: category,
      });
      success = newSetting !== null;
    }

    if (!success) {
      return NextResponse.json(
        { success: false, error: '更新或创建设置失败' },
        { status: 500 }
      );
    }

    const updatedSetting = systemSettingOperations.getByKey(key);
    return NextResponse.json({ success: true, data: updatedSetting });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { success: false, error: '设置键不能为空' },
        { status: 400 }
      );
    }

    // 检查设置是否存在
    const exists = systemSettingOperations.exists(key);
    
    if (!exists) {
      return NextResponse.json(
        { success: false, error: '设置不存在' },
        { status: 404 }
      );
    }

    const success = systemSettingOperations.delete(key);
    if (!success) {
      return NextResponse.json(
        { success: false, error: '删除设置失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, message: '设置已删除' });
  } catch (error) {
    console.error('删除系统设置失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}