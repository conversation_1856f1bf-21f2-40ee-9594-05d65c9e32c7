'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './AuthProvider';
import Loading from './Loading';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && requireAuth && !user) {
      router.push(redirectTo);
    }
  }, [user, loading, requireAuth, redirectTo, router]);

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="flex h-screen bg-theme-background items-center justify-center">
        <Loading 
          size="normal"
          text="正在验证身份..."
          showText={true}
          containerStyle={{
            padding: '3rem'
          }}
        />
      </div>
    );
  }

  // 如果需要认证但用户未登录，不渲染内容（将重定向）
  if (requireAuth && !user) {
    return null;
  }

  // 渲染受保护的内容
  return <>{children}</>;
}