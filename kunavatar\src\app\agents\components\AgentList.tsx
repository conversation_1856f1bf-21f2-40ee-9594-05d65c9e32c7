'use client';

import React from 'react';
import { AgentWithRelations } from '../types';
import { 
  Eye, 
  Trash2, 
  Cpu, 
  Server, 
  Bot, 
  Axe,
  Settings,
} from 'lucide-react';

interface AgentListProps {
  agents: AgentWithRelations[];
  isLoading?: boolean;
  onEdit: (agent: AgentWithRelations) => void;
  onDelete: (agentId: number) => void;
  onShowDetails?: (agent: AgentWithRelations) => void;
}

const AgentList: React.FC<AgentListProps> = ({ 
  agents, 
  isLoading = false, 
  onEdit, 
  onDelete, 
  onShowDetails 
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="flex flex-col items-center gap-4">
          <div className="spinner">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <p className="text-sm font-medium" style={{ color: 'var(--color-foreground-muted)' }}>
            正在加载智能体...
          </p>
        </div>
      </div>
    );
  }

  if (agents.length === 0) {
    return (
      <div className="text-center py-20">
        <div 
          className="rounded-2xl border backdrop-blur-sm p-12"
          style={{ 
            backgroundColor: 'var(--color-card)',
            borderColor: 'var(--color-border)',
            background: `linear-gradient(135deg, 
              var(--color-card) 0%, 
              var(--color-background-secondary) 100%)`
          }}
        >
          <div className="flex flex-col items-center gap-6 max-w-md mx-auto">
            <div 
              className="w-24 h-24 rounded-2xl flex items-center justify-center"
              style={{ 
                background: `linear-gradient(135deg, 
                  rgba(var(--color-primary-rgb), 0.1) 0%, 
                  rgba(var(--color-primary-rgb), 0.05) 100%)`,
                border: `1px solid rgba(var(--color-primary-rgb), 0.2)`
              }}
            >
              <Bot className="w-12 h-12" style={{ color: 'var(--color-primary)' }} />
            </div>
            <div>
              <h3 className="heading-h3 mb-3" style={{ color: 'var(--color-foreground)' }}>
                暂无智能体
              </h3>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
      {agents.map((agent, index) => (
        <div 
          key={agent.id}
          className="group relative overflow-hidden rounded-2xl cursor-pointer"
          style={{
            background: `linear-gradient(145deg, 
              var(--color-card) 0%, 
              var(--color-background-secondary) 100%)`,
          }}
        >
          {/* 背景装饰渐变 */}
          <div 
            className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            style={{
              background: `linear-gradient(135deg, 
                rgba(var(--color-primary-rgb), 0.03) 0%, 
                rgba(var(--color-primary-rgb), 0.08) 50%,
                rgba(var(--color-primary-rgb), 0.03) 100%)`
            }}
          />
          
          {/* 顶部装饰条 */}
          <div 
            className="absolute top-0 left-0 right-0 h-1"
            style={{
              background: `linear-gradient(90deg, 
                var(--color-primary) 0%, 
                var(--color-accent) 100%)`
            }}
          />

          {/* 主要内容区域 */}
          <div className="relative p-6 flex flex-col h-full">
            {/* 头部：头像和基本信息 */}
            <div className="flex items-start gap-4 mb-6">
              <div className="relative w-16 h-16 rounded-xl overflow-hidden transition-all duration-300">
                {agent.avatar ? (
                  <img 
                    src={agent.avatar} 
                    alt={`${agent.name}的头像`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error('头像加载失败:', agent.avatar);
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                {/* 默认头像 - 当没有头像或头像加载失败时显示 */}
                <div 
                  className={`w-full h-full flex items-center justify-center text-white ${agent.avatar ? 'hidden' : ''}`}
                  style={{
                    background: `linear-gradient(135deg, 
                      var(--color-primary) 0%, 
                      var(--color-accent) 100%)`,
                    boxShadow: `
                      0 8px 16px rgba(var(--color-primary-rgb), 0.3),
                      inset 0 1px 0 rgba(255, 255, 255, 0.2)
                    `
                  }}
                >
                  <Bot className="w-8 h-8" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 
                  className="text-lg font-bold truncate mb-1 group-hover:text-primary transition-colors duration-300"
                  style={{ color: 'var(--color-foreground)' }}
                  title={agent.name}
                >
                  {agent.name}
                </h3>
                <div 
                  className="inline-flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium"
                  style={{
                    backgroundColor: 'rgba(var(--color-primary-rgb), 0.1)',
                    color: 'var(--color-primary)',
                    border: `1px solid rgba(var(--color-primary-rgb), 0.2)`
                  }}
                >
                  <Cpu className="w-3 h-3" />
                  {agent.model.display_name}
                </div>
              </div>
            </div>
            
            {/* 描述区域 */}
            <div className="mb-6 flex-grow">
              <p 
                className="text-sm line-clamp-3 leading-relaxed"
                style={{ color: 'var(--color-foreground-secondary)' }}
              >
                {agent.description || '这个智能体还没有添加描述，点击编辑来完善信息。'}
              </p>
            </div>
            
            {/* 统计信息网格 */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              <div 
                className="p-3 rounded-xl"
                style={{
                  backgroundColor: 'var(--color-background-tertiary)',
                  border: `1px solid var(--color-border-secondary)`
                }}
              >
                <div className="flex items-center gap-2 mb-1">
                  <Server className="w-4 h-4" style={{ color: 'var(--color-info)' }} />
                  <span className="text-xs font-medium" style={{ color: 'var(--color-foreground-muted)' }}>
                    服务器
                  </span>
                </div>
                <div className="text-lg font-bold" style={{ color: 'var(--color-foreground)' }}>
                  {agent.servers.length}
                </div>
              </div>
              
              <div 
                className="p-3 rounded-xl"
                style={{
                  backgroundColor: 'var(--color-background-tertiary)',
                  border: `1px solid var(--color-border-secondary)`
                }}
              >
                <div className="flex items-center gap-2 mb-1">
                  <Axe className="w-4 h-4" style={{ color: 'var(--color-success)' }} />
                  <span className="text-xs font-medium" style={{ color: 'var(--color-foreground-muted)' }}>
                    工具
                  </span>
                </div>
                <div className="text-lg font-bold" style={{ color: 'var(--color-foreground)' }}>
                  {agent.tools.length}
                </div>
              </div>
            </div>
            
            {/* 底部操作栏 */}
            <div 
              className="flex items-center justify-end pt-4 border-t"
              style={{ borderColor: 'var(--color-border-secondary)' }}
            >
              <div className="flex items-center gap-1">
                {onShowDetails && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onShowDetails(agent);
                    }}
                    className="p-2 rounded-lg transition-all duration-200 hover:scale-110"
                    style={{
                      color: 'var(--color-foreground-muted)',
                      backgroundColor: 'transparent'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(var(--color-info), 0.1)';
                      e.currentTarget.style.color = 'var(--color-info)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = 'var(--color-foreground-muted)';
                    }}
                    title="查看详情"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                )}
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(agent);
                  }}
                  className="p-2 rounded-lg transition-all duration-200 hover:scale-110"
                  style={{
                    color: 'var(--color-foreground-muted)',
                    backgroundColor: 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(var(--color-warning), 0.1)';
                    e.currentTarget.style.color = 'var(--color-warning)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--color-foreground-muted)';
                  }}
                  title="编辑智能体"
                >
                  <Settings className="w-4 h-4" />
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(agent.id);
                  }}
                  className="p-2 rounded-lg transition-all duration-200 hover:scale-110"
                  style={{
                    color: 'var(--color-foreground-muted)',
                    backgroundColor: 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(var(--color-error), 0.1)';
                    e.currentTarget.style.color = 'var(--color-error)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--color-foreground-muted)';
                  }}
                  title="删除智能体"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* 悬停时的边框光效 */}
          <div 
            className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
            style={{
              border: `1px solid rgba(var(--color-primary-rgb), 0.3)`,
              boxShadow: `
                0 0 20px rgba(var(--color-primary-rgb), 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
              `
            }}
          />
        </div>
      ))}
    </div>
  );
};

export default AgentList;