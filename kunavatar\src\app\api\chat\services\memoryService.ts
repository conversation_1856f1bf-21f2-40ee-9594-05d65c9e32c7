import { ollamaClient, ChatMessage } from '../../../../lib/ollama';
import { dbOperations } from '../../../../lib/database';
import { memoryOperations } from '../../../../lib/database/memories';
import { agentOperations } from '../../../../lib/database/agents';
import { systemSettingOperations } from '../../../../lib/database/system-settings';
import type { ConversationMemory } from '../../../../lib/database/memories';
import type { SystemSetting } from '../../../../lib/database/system-settings';

// 定义全局记忆设置的结构
interface GlobalMemorySettings {
  memory_enabled: boolean;
  memory_model: string;
  memory_trigger_rounds: number;
  max_memory_entries: number;
  summary_style: 'brief' | 'detailed' | 'structured';
  memory_system_prompt: string;
}

interface MemoryContext {
  conversationId: number;
  agentId: number | null;
  messages: ChatMessage[];
  settings: GlobalMemorySettings;
}

interface MemorySummaryResult {
  summary: string;
  importantTopics: string[];
  keyFacts: string[];
  preferences: string[];
  context: string;
}

export class MemoryService {
  /**
   * 检查是否应该触发记忆生成（改进版）
   * 新逻辑:
   * 1. 检查全局记忆开关是否开启
   * 2. 检查当前智能体是否开启记忆
   * 3. 检查是否达到触发轮数（当前对话的新消息）
   */
  static shouldTriggerMemory(conversationId: number, agentId: number | null): boolean {
    if (!agentId) return false;

    // 1. 获取全局设置
    const globalSettings = this.getGlobalMemorySettings();
    if (!globalSettings.memory_enabled) {
      console.log(`🧠 记忆触发检查：全局记忆已关闭`);
      return false; // 全局记忆关闭
    }
    
    // 2. 检查智能体设置
    const agent = agentOperations.getById(agentId);
    if (!agent || !agent.memory_enabled) {
      console.log(`🧠 记忆触发检查：Agent ${agentId} 未启用记忆`);
      return false; // 智能体未启用记忆
    }

    // 3. 检查触发条件（当前对话的轮数）
    const allMessages = dbOperations.getMessagesByConversationId(conversationId);
    const userAssistantMessages = allMessages.filter(msg => 
      msg.role === 'user' || msg.role === 'assistant'
    );
    
    // 获取当前对话的最新记忆
    const conversationMemories = memoryOperations.getMemoriesByConversation(conversationId);
    const lastMemory = conversationMemories.length > 0 ? conversationMemories[0] : null;
    
    // 计算自上次记忆后的新消息数量
    let newMessagesCount = userAssistantMessages.length;
    if (lastMemory && lastMemory.source_message_range) {
      const lastMemoryMessageCount = parseInt(lastMemory.source_message_range.split('-')[1] || '0');
      newMessagesCount = userAssistantMessages.length - lastMemoryMessageCount;
    }
    
    const shouldTrigger = newMessagesCount >= globalSettings.memory_trigger_rounds;
    
    console.log(`🧠 记忆触发检查：对话 ${conversationId}, Agent ${agentId}`);
    console.log(`   - 触发轮数设置: ${globalSettings.memory_trigger_rounds}`);
    console.log(`   - 当前新消息数: ${newMessagesCount}`);
    console.log(`   - 是否触发: ${shouldTrigger}`);
    
    return shouldTrigger;
  }

  /**
   * 为对话生成记忆总结
   */
  static async generateMemory(context: MemoryContext): Promise<ConversationMemory | null> {
    const { conversationId, agentId, messages, settings } = context;

    try {
      console.log(`🧠 开始为对话 ${conversationId} 生成记忆...`);

      // 准备用于总结的消息
      const messagesToSummarize = this.prepareMessagesForSummary(messages, settings);
      
      // 生成记忆总结
      const summary = await this.createMemorySummary(messagesToSummarize, settings);
      
      if (!summary) {
        console.log('❌ 记忆总结生成失败');
        return null;
      }

      // 计算源消息范围
      const sourceRange = this.calculateMessageRange(messages);
      
      // 估算节省的token数量
      const tokensSaved = this.estimateTokensSaved(messagesToSummarize, summary.summary);

      // 存储记忆到数据库
      const memoryId = memoryOperations.createMemory({
        conversation_id: conversationId,
        agent_id: agentId,
        memory_type: 'summary',
        content: JSON.stringify(summary),
        source_message_range: sourceRange,
        importance_score: this.calculateImportanceScore(summary),
        tokens_saved: tokensSaved,
      });

      console.log(`✅ 记忆已创建，ID: ${memoryId}, 节省 token: ${tokensSaved}`);

      // 清理Agent的旧记忆，保持在合理范围内
      if (agentId) {
        const maxAgentMemories = settings.max_memory_entries * 3; // Agent总记忆限制为显示限制的3倍
        const deletedCount = memoryOperations.cleanupAgentMemories(agentId, maxAgentMemories);
        if (deletedCount > 0) {
          console.log(`🧹 自动清理Agent ${agentId} 的 ${deletedCount} 条旧记忆`);
        }
      }

      // 返回创建的记忆
      return memoryOperations.getMemoriesByConversation(conversationId).find(m => m.id === memoryId) || null;

    } catch (error) {
      console.error('记忆生成失败:', error);
      return null;
    }
  }

  /**
   * 获取Agent的记忆上下文（改进：基于Agent而非对话）
   */
  static getMemoryContext(conversationId: number, agentId?: number | null): string {
    const globalSettings = this.getGlobalMemorySettings();
    
    if (!globalSettings.memory_enabled) {
      return '';
    }

    let memories: any[] = [];

    if (agentId) {
      // 优先获取Agent的所有记忆（跨对话）
      memories = memoryOperations.getMemoriesByAgent(agentId);
      console.log(`🧠 获取Agent ${agentId} 的记忆：${memories.length} 条`);
    } else {
      // 回退到对话级别记忆
      memories = memoryOperations.getActiveMemories(conversationId);
      console.log(`🧠 获取对话 ${conversationId} 的记忆：${memories.length} 条`);
    }
    
    if (memories.length === 0) {
      return '';
    }

    // 只保留最新的 N 条记忆
    const memoriesToUse = memories
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, globalSettings.max_memory_entries);

    const memoryTexts = memoriesToUse.map(memory => {
      try {
        const content = JSON.parse(memory.content) as MemorySummaryResult;
        // 显示记忆的来源对话（如果不是当前对话）
        const memorySource = memory.conversation_id !== conversationId 
          ? ` (来自对话 ${memory.conversation_id})` 
          : '';
        return `[记忆 ${memory.id}${memorySource}] ${content.summary}`;
      } catch {
        const memorySource = memory.conversation_id !== conversationId 
          ? ` (来自对话 ${memory.conversation_id})` 
          : '';
        return `[记忆 ${memory.id}${memorySource}] ${memory.content}`;
      }
    });

    return `\n=== Agent记忆 ===\n${memoryTexts.join('\n')}\n=== 记忆结束 ===\n\n`;
  }

  /**
   * 清理对话中的旧消息（保留最近几条）
   */
  static async cleanupOldMessages(conversationId: number, keepRecentCount: number = 10): Promise<void> {
    try {
      const allMessages = dbOperations.getMessagesByConversationId(conversationId);
      
      if (allMessages.length <= keepRecentCount) {
        return; // 消息数量不足，无需清理
      }

      // 按时间戳排序，保留最新的消息
      const sortedMessages = allMessages.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
      const messagesToKeep = sortedMessages.slice(0, keepRecentCount);
      const messagesToDelete = sortedMessages.slice(keepRecentCount);

      console.log(`🧹 清理对话 ${conversationId}，删除 ${messagesToDelete.length} 条旧消息，保留 ${messagesToKeep.length} 条最新消息`);

      // 这里需要在数据库层面实现按ID删除特定消息的功能
      // 暂时跳过具体实现，作为TODO
      console.log('TODO: 实现按消息ID删除的功能');

    } catch (error) {
      console.error('清理旧消息失败:', error);
    }
  }

  /**
   * 准备用于总结的消息
   */
  private static prepareMessagesForSummary(messages: ChatMessage[], settings: GlobalMemorySettings): ChatMessage[] {
    // 过滤掉系统消息和工具调用，只保留用户和助手的对话
    return messages.filter(msg => 
      msg.role === 'user' || msg.role === 'assistant'
    ).slice(-settings.memory_trigger_rounds * 2); // 取最近的对话轮次
  }

  /**
   * 创建记忆总结
   */
  private static async createMemorySummary(
    messages: ChatMessage[], 
    settings: GlobalMemorySettings
  ): Promise<MemorySummaryResult | null> {
    try {
      // 构建总结提示词
      const summaryPrompt = this.buildSummaryPrompt(messages, settings);
      
      // 选择总结模型
      const summaryModel = settings.memory_model || 'qwen2.5:3b'; // 默认使用轻量级模型
      
      const response = await ollamaClient.chat({
        model: summaryModel,
        messages: [
          {
            role: 'system',
            content: settings.memory_system_prompt || this.getDefaultMemoryPrompt(settings.summary_style)
          },
          {
            role: 'user',
            content: summaryPrompt
          }
        ],
        stream: false,
        options: {
          temperature: 0.3, // 较低的温度确保总结的一致性
          top_p: 0.8,
        }
      });

      if (!response.message?.content) {
        return null;
      }

      // 尝试解析JSON格式的响应
      try {
        return JSON.parse(response.message.content);
      } catch {
        // 如果不是JSON格式，创建简单的总结结构
        return {
          summary: response.message.content,
          importantTopics: [],
          keyFacts: [],
          preferences: [],
          context: response.message.content
        };
      }

    } catch (error) {
      console.error('创建记忆总结失败:', error);
      return null;
    }
  }

  /**
   * 构建总结提示词
   */
  private static buildSummaryPrompt(messages: ChatMessage[], settings: GlobalMemorySettings): string {
    const conversationText = messages.map(msg => 
      `${msg.role === 'user' ? '用户' : '助手'}: ${msg.content}`
    ).join('\n');

    return `请总结以下对话内容，提取关键信息：

对话内容：
${conversationText}

请以JSON格式返回总结，包含以下字段：
- summary: 对话的主要内容总结
- importantTopics: 重要话题列表
- keyFacts: 关键事实信息
- preferences: 用户偏好或需求
- context: 对话上下文信息

总结风格：${settings.summary_style === 'brief' ? '简洁' : settings.summary_style === 'detailed' ? '详细' : '结构化'}`;
  }

  /**
   * 获取默认记忆提示词
   */
  private static getDefaultMemoryPrompt(style: string): string {
    const basePrompt = `你是一个专业的对话记忆助手。你的任务是从用户和AI助手的对话中提取和整理重要信息，形成结构化的记忆。`;

    switch (style) {
      case 'brief':
        return `${basePrompt}\n\n要求：\n- 提取核心要点，保持简洁\n- 重点关注关键决定和重要信息\n- 忽略闲聊和无关内容`;
      
      case 'structured':
        return `${basePrompt}\n\n要求：\n- 按类别组织信息（事实、偏好、决定等）\n- 使用结构化格式\n- 标记信息的重要程度`;
      
      default: // detailed
        return `${basePrompt}\n\n要求：\n- 全面记录对话内容\n- 保留重要的上下文信息\n- 记录用户的偏好和需求\n- 包含关键的决策过程`;
    }
  }

  /**
   * 计算消息范围
   */
  private static calculateMessageRange(messages: ChatMessage[]): string {
    if (messages.length === 0) return '0-0';
    return `1-${messages.length}`;
  }

  /**
   * 估算节省的token数量
   */
  private static estimateTokensSaved(originalMessages: ChatMessage[], summary: string): number {
    // 简单估算：每个字符约等于0.75个token（中文）
    const originalLength = originalMessages.reduce((sum, msg) => sum + msg.content.length, 0);
    const summaryLength = summary.length;
    return Math.max(0, Math.round((originalLength - summaryLength) * 0.75));
  }

  /**
   * 计算重要性评分
   */
  private static calculateImportanceScore(summary: MemorySummaryResult): number {
    let score = 0.5; // 基础分数

    // 根据内容丰富程度调整分数
    if (summary.importantTopics.length > 0) score += 0.1;
    if (summary.keyFacts.length > 0) score += 0.1;
    if (summary.preferences.length > 0) score += 0.1;
    if (summary.summary.length > 100) score += 0.1;
    if (summary.summary.length > 300) score += 0.1;

    return Math.min(1.0, score);
  }

  /**
   * 获取全局记忆设置
   */
  static getGlobalMemorySettings(): GlobalMemorySettings {
    const settings = systemSettingOperations.getByCategory('memory');
    const settingsMap = new Map(settings.map(s => [s.key, s.value]));

    return {
      memory_enabled: settingsMap.get('memory_enabled') === '1',
      memory_model: String(settingsMap.get('memory_model') || 'qwen2.5:3b'),
      memory_trigger_rounds: parseInt(String(settingsMap.get('memory_trigger_rounds') || '20'), 10),
      max_memory_entries: parseInt(String(settingsMap.get('max_memory_entries') || '10'), 10),
      summary_style: (settingsMap.get('summary_style') as any) || 'detailed',
      memory_system_prompt: String(settingsMap.get('memory_system_prompt') || ''),
    };
  }

  /**
   * 更新全局记忆设置
   */
  static updateGlobalMemorySetting(key: string, value: string): boolean {
    return systemSettingOperations.setValue(key, value);
  }
} 