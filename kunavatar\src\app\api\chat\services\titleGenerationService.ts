import { dbOperations } from '../../../../lib/database';

export interface TitleSummarySettings {
  enabled: boolean;
  model: string;
}

/**
 * 检查并生成对话标题的服务
 */
export class TitleGenerationService {
  /**
   * 检查并生成对话标题
   */
  static async checkAndGenerateTitle(
    conversationId: number, 
    titleSummarySettings?: TitleSummarySettings
  ): Promise<string | null> {
    try {
      // 检查是否启用标题总结功能
      if (!titleSummarySettings?.enabled || !titleSummarySettings?.model) {
        return null;
      }

      // 获取对话信息
      const conversation = dbOperations.getConversationById(conversationId);
      if (!conversation) {
        return null;
      }

      // 检查是否已经有自定义标题（不是默认的"新对话"或带时间戳的默认标题）
      const isDefaultTitle = conversation.title === '新对话' || conversation.title.startsWith('新对话 - ');
      if (!isDefaultTitle) {
        return null; // 已经有自定义标题，不需要重新生成
      }

      // 获取对话消息
      const messages = dbOperations.getMessagesByConversationId(conversationId);
      const userMessages = messages.filter(m => m.role === 'user');
      const assistantMessages = messages.filter(m => m.role === 'assistant');

      // 检查是否有足够的消息（至少一轮对话）
      if (userMessages.length === 0 || assistantMessages.length === 0) {
        return null;
      }

      // 同步生成标题
      return await this.generateTitle(conversationId, titleSummarySettings.model);
    } catch (error) {
      console.error('检查标题生成条件时出错:', error);
      return null;
    }
  }

  /**
   * 调用标题生成API
   */
  private static async generateTitle(conversationId: number, model: string): Promise<string | null> {
    try {
      const response = await fetch(`http://localhost:3000/api/conversations/${conversationId}/generate-title`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ model })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ 标题生成成功:', result.title);
        return result.title;
      } else {
        console.warn('⚠️ 标题生成失败:', await response.text());
        return null;
      }
    } catch (error) {
      console.error('❌ 标题生成请求失败:', error);
      return null;
    }
  }

  /**
   * 发送标题更新事件到流式响应
   */
  static sendTitleUpdateEvent(
    controller: ReadableStreamDefaultController<Uint8Array>,
    encoder: TextEncoder,
    conversationId: number,
    title: string
  ): void {
    const titleUpdateData = JSON.stringify({
      type: 'title_update',
      conversationId: conversationId,
      title: title
    });
    try {
      controller.enqueue(encoder.encode(`data: ${titleUpdateData}\n\n`));
    } catch (e) {
      console.log('流已关闭，无法发送标题更新事件');
    }
  }
} 