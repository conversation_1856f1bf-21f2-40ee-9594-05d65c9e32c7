import { NextRequest, NextResponse } from 'next/server';
import { multiServerMcpClient } from '@/lib/mcp/mcp-multi-server-client';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 执行本地工具
 */
async function executeToolLocally(name: string, args: any): Promise<any> {
  switch (name) {
    case 'calculate':
      return executeCalculate(args);
    case 'get_current_time':
      return executeGetCurrentTime(args);
    default:
      return null; // 不是本地工具
  }
}

/**
 * 计算器工具
 */
function executeCalculate(args: { expression?: string }) {
  try {
    const expression = args.expression;
    if (!expression) {
      throw new Error('表达式不能为空');
    }
    
    // 简单的数学表达式计算（安全起见，只支持基本运算）
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
    const result = Function('"use strict"; return (' + sanitized + ')')();
    
    return {
      content: [{
        type: 'text',
        text: `计算结果: ${expression} = ${result}`
      }]
    };
  } catch (error) {
    return {
      content: [{
        type: 'text',
        text: `计算错误: ${error instanceof Error ? error.message : '未知错误'}`
      }]
    };
  }
}

/**
 * 获取当前时间工具
 */
function executeGetCurrentTime(args: any) {
  try {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    return {
      content: [{
        type: 'text',
        text: `当前时间: ${timeString}`
      }]
    };
  } catch (error) {
    return {
      content: [{
        type: 'text',
        text: `获取时间失败: ${error instanceof Error ? error.message : '未知错误'}`
      }]
    };
  }
}

/**
 * MCP工具调用API
 * POST /api/mcp/call-tool
 */
export async function POST(request: NextRequest) {
  try {
    const { name, arguments: args, serverName } = await request.json();
    
    if (!name) {
      return NextResponse.json(
        { success: false, error: '工具名称不能为空' },
        { status: 400 }
      );
    }

    // 读取服务器配置
    const configPath = path.join(process.cwd(), 'mcp-servers.json');
    let config = {};
    
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf-8');
      const parsedConfig = JSON.parse(configContent);
      config = parsedConfig.mcpServers || {};
    }
    
    // 设置配置
    multiServerMcpClient.setConfig(config);
    
    // 首先尝试本地工具
    const localResult = await executeToolLocally(name, args || {});
    if (localResult) {
      return NextResponse.json({
        success: true,
        result: localResult,
        serverName: 'local'
      });
    }
    
    // 智能连接：只连接包含指定工具的服务器
    let targetServerName = serverName;
    
    if (serverName && serverName !== 'local') {
      // 如果指定了服务器名称，检查该服务器是否已连接
      const connectionStatus = multiServerMcpClient.getConnectionStatus();
      if (!connectionStatus[serverName]) {
        console.log(`连接指定的MCP服务器: ${serverName}`);
        const connected = await multiServerMcpClient.connectServer(serverName);
        if (!connected) {
          return NextResponse.json(
            { success: false, error: `无法连接到服务器 '${serverName}'` },
            { status: 500 }
          );
        }
      }
    } else {
      // 如果没有指定服务器，使用智能连接来找到包含该工具的服务器
      console.log(`智能查找工具 '${name}' 所在的服务器`);
      targetServerName = await multiServerMcpClient.connectForTool(name);
      
      if (!targetServerName) {
        return NextResponse.json(
          { success: false, error: `找不到包含工具 '${name}' 的服务器` },
          { status: 404 }
        );
      }
    }

    // 调用工具（使用确定的服务器名称）
    const result = await multiServerMcpClient.callTool(name, args || {}, targetServerName);
    
    return NextResponse.json({
      success: true,
      result,
      serverName: targetServerName || 'auto-detected'
    });
  } catch (error) {
    console.error('MCP多服务器工具调用失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '工具调用失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 硬编码工具已移除，现在只使用MCP服务器提供的工具