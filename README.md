# Kun Avatar

<div align="center">
  <h3>🤖 基于 Next.js 的智能对话助手应用</h3>
  <p>集成 Ollama 模型管理、MCP 工具调用和智能体系统的现代化 AI 聊天平台</p>
</div>

## 📋 项目概览

**Kun Agent** 是一个功能强大的智能对话助手应用，基于 Next.js 15 和 React 19 构建，提供完整的 AI 聊天体验。项目集成了 Ollama 模型管理、Model Context Protocol (MCP) 工具调用支持，以及可配置的智能体系统。

### 🎯 核心特性

- 🤖 **智能对话**: 支持流式对话，实时响应
- 🧠 **模型管理**: 完整的 Ollama 模型配置和管理系统
- 🔧 **MCP 集成**: Model Context Protocol 工具调用支持
- 👥 **智能体系统**: 可配置的 AI 智能体管理
- 🎨 **现代化 UI**: 响应式设计，深浅主题切换
- 📚 **对话历史**: SQLite 数据库持久化存储
- 🛠️ **工具调用**: 支持多种外部工具集成
- 🌐 **多模型支持**: 兼容各种 Ollama 模型

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- Ollama 服务（用于 AI 模型支持）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Kun-Avatar
   ```

2. **进入项目目录**
   ```bash
   cd kunavatar
   ```

3. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   # 或
   yarn dev
   ```

5. **访问应用**
   
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### MCP 服务器启动（可选）

如果需要使用 MCP 工具功能：

```bash
# 启动 MCP 服务器
npm run mcp-server

# 或开发模式
npm run mcp-dev
```

## 🏗️ 技术架构

### 技术栈

**前端框架**:
- Next.js 15.3.3 (React 框架)
- React 19.1.0 (UI 库)
- TypeScript 5.x (类型系统)

**样式和 UI**:
- Tailwind CSS 3.4.0 (样式框架)
- Framer Motion 12.18.1 (动画库)
- Lucide React 0.460.0 (图标库)

**数据库和存储**:
- better-sqlite3 11.5.0 (SQLite 数据库)
- sqlite3 5.1.7 (SQLite 驱动)

**AI 和工具**:
- @modelcontextprotocol/sdk 1.12.1 (MCP 协议)
- zod 3.22.0 (数据验证)

### 项目结构

```
kunavatar/
├── src/
│   ├── app/                   # Next.js App Router 页面
│   │   ├── api/               # API 路由
│   │   ├── simple-chat/       # 聊天页面
│   │   ├── model-manager/     # 模型管理页面
│   │   ├── mcp-config/        # MCP 配置页面
│   │   ├── agents/            # 智能体管理页面
│   │   ├── settings/          # 设置页面
│   │   └── conversations/     # 对话历史页面
│   ├── components/            # 共享组件
│   ├── lib/                   # 核心库
│   │   ├── database/          # 数据库操作
│   │   ├── mcp/               # MCP 客户端
│   │   ├── ollama.ts          # Ollama API 客户端
│   │   ├── theme.ts           # 主题系统
│   │   └── tools.ts           # 工具管理
│   └── theme/                 # 主题相关组件
├── public/                    # 静态资源
├── package.json               # 依赖配置
├── next.config.js             # Next.js 配置
├── tailwind.config.js         # Tailwind CSS 配置
└── tsconfig.json              # TypeScript 配置
```

## 🔧 功能模块

### 1. 聊天系统
- 流式对话支持
- 工具调用集成
- 模型切换
- 智能体选择
- 对话历史管理

### 2. 模型管理
- Ollama 模型同步
- 自定义模型创建
- 中文模型名称支持
- 模型参数配置
- Modelfile 支持

### 3. MCP 集成
- 标准 MCP 协议支持
- 多服务器管理
- 工具调用
- 资源管理

### 4. 智能体系统
- 可配置的 AI 智能体
- 自定义系统提示词
- 智能体模板管理

### 5. 主题系统
- 深浅主题切换
- 系统主题跟随
- 持久化存储
- 平滑过渡动画

## 📚 API 文档

### 核心 API 端点

- `POST /api/chat` - 聊天对话接口
- `GET /api/models` - 获取模型列表
- `GET/POST /api/custom-models` - 自定义模型管理
- `GET/POST /api/mcp/servers` - MCP 服务器管理
- `GET /api/mcp/tools` - MCP 工具列表
- `POST /api/mcp/call-tool` - 工具调用
- `GET/POST /api/agents` - 智能体管理
- `GET/POST /api/conversations` - 对话管理

## 🛠️ 开发指南

### 可用脚本

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# MCP 服务器相关
npm run mcp-server    # 启动 MCP 服务器
npm run mcp-dev       # MCP 开发模式
npm run mcp-ts        # TypeScript MCP 服务器
```

### 环境配置

确保 Ollama 服务正在运行：

```bash
# 安装 Ollama（如果尚未安装）
# 访问 https://ollama.ai 获取安装指南

# 启动 Ollama 服务
ollama serve

# 拉取模型（示例）
ollama pull llama2
ollama pull qwen:7b
```

### 数据库

项目使用 SQLite 数据库，数据库文件位于 `chat.db`。首次运行时会自动创建必要的表结构。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Ollama](https://ollama.ai/) - 本地 AI 模型运行时
- [Model Context Protocol](https://modelcontextprotocol.io/) - AI 工具集成协议
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Lucide](https://lucide.dev/) - 图标库

## 📞 支持

如果您遇到任何问题或有任何建议，请：

1. 查看项目文档
2. 搜索现有的 Issues
3. 创建新的 Issue 描述问题

---

<div align="center">
  <p>Made with ❤️ by Kun Agent Team</p>
</div>