'use client';

import React from 'react';
import { Conversation } from '../../../../lib/database';
import { CustomModel } from '@/lib/database/custom-models';
import { SimpleMessage } from '../../types';
import { ChatHeader } from '../conversation/ChatHeader';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { ErrorDisplay } from '../ui/ErrorDisplay';
import { ToolSettings } from '../tools/ToolSettings';
import { ChatStyle, DisplaySize } from '../input-controls';
import { AgentWithRelations } from '@/app/agents/types';
import { Bot } from 'lucide-react';

type SelectorMode = 'model' | 'agent';

interface ChatContainerProps {
  // 对话相关
  currentConversation: Conversation | null;
  
  // 模型相关
  models: CustomModel[];
  selectedModel: string;
  onModelChange: (model: string) => void;

  // 智能体相关
  agents: AgentWithRelations[];
  selectedAgentId: number | null;
  onAgentChange: (agentId: number | null) => void;
  selectorMode: SelectorMode;
  onSelectorModeChange: (mode: SelectorMode) => void;
  
  // 自定义模型显示信息 - 重新添加以支持正确的模型显示
  customModels: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  
  // 消息相关
  messages: SimpleMessage[];
  inputMessage: string;
  onInputChange: (message: string) => void;
  onSendMessage: () => void;
  isStreaming: boolean;
  onStopGeneration: () => void;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  
  // 工具相关
  enableTools: boolean;
  selectedTools: string[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: string[]) => void;
  onInsertText: (text: string) => void;
  onClearChat: () => void;
  
  // 错误处理
  error: string | null;
  onDismissError: () => void;
  
  // 聊天样式
  chatStyle: ChatStyle;
  displaySize?: DisplaySize;
  onChatStyleChange: (style: ChatStyle) => void;
  onDisplaySizeChange?: (size: DisplaySize) => void;
  
  // 记忆面板
  isMemoryVisible?: boolean;
  onMemoryToggle?: () => void;
}

export function ChatContainer({
  currentConversation,
  models,
  selectedModel,
  onModelChange,
  agents,
  selectedAgentId,
  onAgentChange,
  selectorMode,
  onSelectorModeChange,
  customModels,
  messages,
  inputMessage,
  onInputChange,
  onSendMessage,
  isStreaming,
  onStopGeneration,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  enableTools,
  selectedTools,
  onToolsToggle,
  onSelectedToolsChange,
  onInsertText,
  onClearChat,
  error,
  onDismissError,
  chatStyle,
  displaySize,
  onChatStyleChange,
  onDisplaySizeChange,
  isMemoryVisible = false,
  onMemoryToggle
}: ChatContainerProps) {
  return (
    <div className="flex-1 flex flex-col min-w-0">
      {/* 顶部标题栏 - 始终显示 */}
      <ChatHeader
        currentConversation={currentConversation}
        models={models}
        selectedModel={selectedModel}
        onModelChange={onModelChange}
        agents={agents}
        selectedAgentId={selectedAgentId}
        onAgentChange={onAgentChange}
        selectorMode={selectorMode}
        onSelectorModeChange={onSelectorModeChange}
      />

      {/* 聊天消息区域 - 滚动优化 */}
      <div className="flex-1 overflow-hidden relative">
        {messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center px-4 h-full">
            <div className="text-center max-w-md mx-auto">
              <Bot className="w-16 h-16 text-theme-foreground-muted mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-theme-foreground mb-2">
                Kun Avatar
              </h2>
              <p className="text-theme-foreground-muted mb-6">
                智能对话助手，在下方输入框中输入消息开始对话
              </p>
              
             
            </div>
          </div>
        ) : (
          <div className="absolute inset-0 overflow-y-auto scrollbar-thin">
            <div className="flex justify-center min-h-full">
              <div className={`w-full chat-container-responsive ${displaySize || 'compact'}`}>
                <MessageList
                  messages={messages}
                  isStreaming={isStreaming}
                  expandedThinkingMessages={expandedThinkingMessages}
                  onToggleThinkingExpand={onToggleThinkingExpand}
                  chatStyle={chatStyle}
                  selectedModel={selectedModel}
                  customModels={customModels}
                  selectedAgent={selectedAgentId ? agents.find(a => a.id === selectedAgentId) : null}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 - 添加居中和宽度控制 */}
      {error && (
        <div className="flex justify-center">
          <div className={`w-full px-4 mb-4 chat-container-responsive ${displaySize || 'compact'}`}>
            <ErrorDisplay 
              message={error} 
              onDismiss={onDismissError}
            />
          </div>
        </div>
      )}

      {/* 输入区域 - 添加居中和宽度控制 */}
      <div className="border-t border-theme-border">
        <div className="flex justify-center">
          <div className={`w-full p-4 chat-container-responsive ${displaySize || 'compact'}`}>
            {/* 工具设置组件 */}
            <ToolSettings
              selectedModel={selectedModel}
              enableTools={enableTools}
              selectedTools={selectedTools}
              onToolsToggle={onToolsToggle}
              onSelectedToolsChange={onSelectedToolsChange}
              onInsertText={onInsertText}
              onClearChat={onClearChat}
              chatStyle={chatStyle}
              displaySize={displaySize}
              onChatStyleChange={onChatStyleChange}
              onDisplaySizeChange={onDisplaySizeChange}
              isMemoryVisible={isMemoryVisible}
              onMemoryToggle={onMemoryToggle}
              conversationId={currentConversation?.id}
              selectedAgentId={selectedAgentId}
            />
            
            {/* 消息输入组件 */}
            <MessageInput
              inputMessage={inputMessage}
              onInputChange={onInputChange}
              onSendMessage={onSendMessage}
              onStopGeneration={onStopGeneration}
              isStreaming={isStreaming}
              currentConversation={currentConversation}
              selectedModel={selectedModel}
            />
          </div>
        </div>
      </div>
    </div>
  );
}