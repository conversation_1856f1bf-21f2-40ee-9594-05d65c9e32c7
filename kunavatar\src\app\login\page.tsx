'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Eye, EyeOff, LogIn } from 'lucide-react';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 保存访问令牌
        localStorage.setItem('accessToken', data.accessToken);
        
        // 重定向到设置页面或首页
        const redirectTo = new URLSearchParams(window.location.search).get('redirect') || '/settings';
        router.push(redirectTo);
      } else {
        setError(data.error || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      setError('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // 对用户名和密码字段去除空格
    const trimmedValue = (name === 'username' || name === 'password') ? value.trim() : value;
    
    setFormData(prev => ({
      ...prev,
      [name]: trimmedValue,
    }));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-theme-background-secondary py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-theme-primary/10">
            <LogIn className="h-6 w-6 text-theme-primary" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-theme-foreground">
            登录到您的账户
          </h2>
          <p className="mt-2 text-center text-sm text-theme-foreground-muted">
            或者{' '}
            <Link href="/register" className="font-medium text-theme-primary hover:text-theme-primary-hover">
              创建新账户
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-theme-foreground">
                用户名或邮箱
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                value={formData.username}
                onChange={handleChange}
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                placeholder="请输入用户名或邮箱"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-theme-foreground">
                密码
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-theme-border placeholder-theme-foreground-muted text-theme-foreground rounded-md focus:outline-none focus:ring-theme-primary focus:border-theme-primary focus:z-10 sm:text-sm bg-theme-background"
                  placeholder="请输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-theme-foreground-muted" />
                  ) : (
                    <Eye className="h-4 w-4 text-theme-foreground-muted" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="text-sm">
              <Link href="/forgot-password" className="font-medium text-theme-primary hover:text-theme-primary-hover">
                忘记密码？
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-theme-primary hover:bg-theme-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? '登录中...' : '登录'}
            </button>
          </div>
        </form>

        {/* 演示账户信息 */}
        <div className="mt-6 p-4 bg-theme-card border border-theme-border rounded-md">
          <h3 className="text-sm font-medium text-theme-foreground mb-2">演示账户</h3>
          <div className="text-sm text-theme-foreground-muted">
            <p>用户名: admin</p>
            <p>密码: 123456</p>
          </div>
        </div>
      </div>
    </div>
  );
}
