'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Sun, Moon, Settings as SettingsIcon } from 'lucide-react';
import { useTheme } from '@/theme/contexts/ThemeContext';

interface AppearanceTabProps {
  // 可以接收一些props
}

export function AppearanceTab({}: AppearanceTabProps) {
  const { theme: contextTheme, setTheme: setContextTheme } = useTheme();
  const [themePreference, setThemePreference] = useState<'light' | 'dark' | 'system'>('system');
  const [colorTheme, setColorTheme] = useState<string>('kun');
  const [fontSize, setFontSize] = useState<'small' | 'medium' | 'large'>('medium');
  const [compactMode, setCompactMode] = useState(false);

  // 可用的颜色主题（从ColorThemeSwitcher移植）
  const colorThemes = [
    { name: 'kun', color: '#6a5ac2', displayName: '默认' },
    { name: 'green', color: '#4D564F', displayName: '芦苇绿' },
    { name: 'purple', color: '#6a5ac2', displayName: '优雅紫' },
    { name: 'orange', color: '#E07800', displayName: '活力橙' },
    { name: 'blue', color: '#284B7B', displayName: '伯克利蓝' },
    { name: 'raspberry', color: '#EC4680', displayName: '覆盆子' },
    { name: 'moonstone', color: '#61A0AF', displayName: '月长石' },
  ];

  // 从本地存储加载设置
  useEffect(() => {
    const savedThemePreference = localStorage.getItem('theme-preference') as 'light' | 'dark' | 'system' | null;
    const savedColorTheme = localStorage.getItem('color-theme') || 'kun';
    const savedFontSize = localStorage.getItem('fontSize') as 'small' | 'medium' | 'large' | null;
    const savedCompactMode = localStorage.getItem('compactMode') === 'true';

    if (savedThemePreference) {
      setThemePreference(savedThemePreference);
      if (savedThemePreference === 'system') {
        // 如果是系统主题，立即应用当前系统偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        setContextTheme(prefersDark ? 'dark' : 'light');
      }
    }
    setColorTheme(savedColorTheme);
    if (savedFontSize) setFontSize(savedFontSize);
    setCompactMode(savedCompactMode);
  }, [setContextTheme]);

  // 更新主题（使用ThemeContext）
  const updateTheme = (newTheme: 'light' | 'dark' | 'system') => {
    setThemePreference(newTheme);
    
    if (newTheme === 'system') {
      // 检测系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setContextTheme(prefersDark ? 'dark' : 'light');
      
      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        setContextTheme(e.matches ? 'dark' : 'light');
      };
      mediaQuery.addEventListener('change', handleChange);
      
      // 存储系统主题偏好
      localStorage.setItem('theme-preference', 'system');
    } else {
      setContextTheme(newTheme);
      localStorage.setItem('theme-preference', newTheme);
    }
  };

  // 更新颜色主题（从ColorThemeSwitcher移植）
  const updateColorTheme = (themeName: string) => {
    document.documentElement.setAttribute('data-color-theme', themeName);
    localStorage.setItem('color-theme', themeName);
    setColorTheme(themeName);
  };

  // 更新字体大小
  const updateFontSize = (newFontSize: 'small' | 'medium' | 'large') => {
    setFontSize(newFontSize);
    localStorage.setItem('fontSize', newFontSize);
    
    // 应用字体大小逻辑
    const root = document.documentElement;
    root.setAttribute('data-font-size', newFontSize);
  };

  // 更新紧凑模式
  const updateCompactMode = (enabled: boolean) => {
    setCompactMode(enabled);
    localStorage.setItem('compactMode', enabled.toString());
    
    // 应用紧凑模式逻辑
    const root = document.documentElement;
    root.classList.toggle('compact', enabled);
  };

  return (
    <div className="bg-theme-background">
      <div className="space-y-6">
        {/* 标题 */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-theme-foreground flex items-center gap-2">
            <Palette className="w-5 h-5" />
            界面设置
          </h2>
        </div>
        
        {/* 主题设置 */}
        <div className="bg-theme-background-secondary rounded-lg p-4 border border-theme-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-theme-foreground mb-1">界面主题色</h3>
              <p className="text-sm text-theme-foreground-muted">选择浅色或深色主题</p>
            </div>
            <div className="flex gap-2">
              {[
                { value: 'light', label: '浅色', icon: Sun },
                { value: 'dark', label: '深色', icon: Moon },
                { value: 'system', label: '跟随系统', icon: Monitor }
              ].map(({ value, label, icon: Icon }) => (
                <button
                  key={value}
                  onClick={() => updateTheme(value as 'light' | 'dark' | 'system')}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    themePreference === value
                      ? 'bg-theme-primary text-white'
                      : 'bg-theme-background border border-theme-border text-theme-foreground hover:bg-theme-background/80'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 颜色方案 */}
        <div className="bg-theme-background-secondary rounded-lg p-4 border border-theme-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-theme-foreground mb-1">元素主题色</h3>
              <p className="text-sm text-theme-foreground-muted">选择应用的主题色调</p>
            </div>
            <div className="flex gap-2 flex-wrap">
              {colorThemes.map((theme) => (
                <button
                  key={theme.name}
                  onClick={() => updateColorTheme(theme.name)}
                  title={theme.displayName}
                  className={`relative group w-8 h-8 rounded-full transition-all duration-200 ${colorTheme === theme.name ? 'transform scale-110 ring-2 ring-theme-primary ring-offset-2 ring-offset-theme-background' : 'hover:scale-110'}`}
                  style={{ 
                    backgroundColor: theme.color
                  }}
                >
                  <span className="absolute top-full mt-1 left-1/2 -translate-x-1/2 px-2 py-1 rounded-md text-xs bg-gray-900/80 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
                    {theme.displayName}
                  </span>
                </button>
              ))}
            </div>
          </div>

        </div>

        {/* 字体大小 */}
        <div className="bg-theme-background-secondary rounded-lg p-4 border border-theme-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-theme-foreground mb-1">字体大小</h3>
              <p className="text-sm text-theme-foreground-muted">调整界面文字大小</p>
            </div>
            <div className="flex gap-2">
              {[
                { value: 'small', label: '小' },
                { value: 'medium', label: '中' },
                { value: 'large', label: '大' }
              ].map(({ value, label }) => (
                <button
                  key={value}
                  onClick={() => updateFontSize(value as 'small' | 'medium' | 'large')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    fontSize === value
                      ? 'bg-theme-primary text-white'
                      : 'bg-theme-background border border-theme-border text-theme-foreground hover:bg-theme-background/80'
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 紧凑模式 */}
        <div className="bg-theme-background-secondary rounded-lg p-4 border border-theme-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-theme-foreground">紧凑模式</h3>
              <p className="text-sm text-theme-foreground-muted">减少界面元素间距，显示更多内容</p>
            </div>
            <button
              onClick={() => updateCompactMode(!compactMode)}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2 ${
                compactMode ? 'bg-theme-primary' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  compactMode ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="bg-theme-background-secondary rounded-lg p-4 border border-theme-border">
          <h3 className="text-lg font-medium text-theme-foreground mb-4">预览</h3>
          <div className="bg-theme-background rounded-lg p-4 border border-theme-border">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 rounded-full bg-theme-primary flex items-center justify-center">
                <span className="text-white text-sm font-medium">A</span>
              </div>
              <div>
                <div className="text-sm font-medium text-theme-foreground">示例用户</div>
                <div className="text-xs text-theme-foreground-muted">这是一个预览示例</div>
              </div>
            </div>
            <div className="text-sm text-theme-foreground">
              这里展示当前主题设置的效果。您可以看到文字大小、颜色搭配和整体风格。
            </div>
            <div className="mt-3 flex gap-2">
              <button className="px-3 py-1 text-xs bg-theme-primary text-white rounded-md">
                主要按钮
              </button>
              <button className="px-3 py-1 text-xs bg-theme-background-secondary text-theme-foreground border border-theme-border rounded-md">
                次要按钮
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}