'use client';

import React from 'react';
import { useToolSettings } from '../../hooks/useToolSettings';
import { ToolPanel } from './ToolPanel';
import { MemoryPanel } from '../memory/MemoryPanel';
import { InputControlsGroup, ChatStyle, DisplaySize } from '../input-controls';

interface ToolSettingsProps {
  selectedModel: string;
  enableTools: boolean;
  selectedTools: string[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: string[]) => void;
  onInsertText: (text: string) => void;
  onClearChat?: () => void;
  chatStyle: ChatStyle;
  displaySize?: DisplaySize;
  onChatStyleChange: (style: ChatStyle) => void;
  onDisplaySizeChange?: (size: DisplaySize) => void;
  
  // 记忆相关
  isMemoryVisible?: boolean;
  onMemoryToggle?: () => void;
  conversationId?: number | null;
  selectedAgentId?: number | null;
}

export function ToolSettings({
  selectedModel,
  enableTools,
  selectedTools,
  onToolsToggle,
  onSelectedToolsChange,
  onInsertText,
  onClearChat,
  chatStyle,
  displaySize,
  onChatStyleChange,
  onDisplaySizeChange,
  isMemoryVisible,
  onMemoryToggle,
  conversationId,
  selectedAgentId,
}: ToolSettingsProps) {
  const {
    showToolSettings,
    setShowToolSettings,
    modelSupportsTools,
    isCheckingModel,
    allTools,
    handleToolsToggle,
    handleToolSelection,
  } = useToolSettings({
    selectedModel,
    enableTools,
    selectedTools,
    onToolsToggle,
    onSelectedToolsChange,
  });

  return (
    <div className="relative">
      {/* 记忆面板 - 显示在输入框上方 */}
       {isMemoryVisible && onMemoryToggle && (
         <div className="absolute bottom-full left-0 right-0 mb-6 z-[60]">
           <MemoryPanel 
             conversationId={conversationId || null}
             agentId={selectedAgentId}
             isVisible={isMemoryVisible}
             onToggle={onMemoryToggle}
           />
         </div>
       )}
       
       {/* 工具面板 - 显示在输入框上方 */}
        {enableTools && showToolSettings && (
          <div className={`absolute bottom-full left-0 right-0 z-50 ${
            isMemoryVisible ? 'mb-[320px]' : 'mb-6'
          }`}>
          <ToolPanel
            allTools={allTools}
            selectedTools={selectedTools}
            onToolSelection={handleToolSelection}
          />
        </div>
      )}
      
      {/* 统一的输入控制组 */}
      <div className="mb-3">
        <InputControlsGroup
          chatStyle={chatStyle}
          displaySize={displaySize}
          onChatStyleChange={onChatStyleChange}
          onDisplaySizeChange={onDisplaySizeChange}
          enableTools={enableTools}
          isCheckingModel={isCheckingModel}
          modelSupportsTools={modelSupportsTools}
          showToolSettings={showToolSettings}
          selectedToolsCount={selectedTools.length}
          onToolsToggle={handleToolsToggle}
          onShowToolSettings={setShowToolSettings}
          isMemoryVisible={isMemoryVisible}
          onMemoryToggle={onMemoryToggle}
          conversationId={conversationId}
          onInsertText={onInsertText}
          onClearChat={onClearChat}
        />
      </div>
    </div>
  );
}