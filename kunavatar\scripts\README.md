# 超级管理员初始化脚本

这个脚本用于初始化 Kun Avatar 系统的超级管理员账号、角色和权限。

## 功能特性

- ✅ 创建超级管理员用户账号
- ✅ 创建超级管理员角色
- ✅ 创建基础权限系统
- ✅ 自动分配所有权限给超级管理员
- ✅ 支持自定义密码
- ✅ 支持强制重新初始化
- ✅ 完整的错误处理和日志输出

## 使用方法

### 基础使用

```bash
# 在项目根目录下运行
node scripts/init-admin.js
```

### 高级选项

```bash
# 使用自定义密码
node scripts/init-admin.js --password=YourSecurePassword123!

# 强制重新初始化（会覆盖现有的超级管理员）
node scripts/init-admin.js --force

# 同时使用自定义密码和强制重新初始化
node scripts/init-admin.js --force --password=YourSecurePassword123!
```

### 通过 npm scripts 运行

```bash
# 基础初始化
npm run init-admin

# 使用自定义密码
npm run init-admin -- --password=YourSecurePassword123!

# 强制重新初始化
npm run init-admin -- --force
```

## 默认配置

脚本会创建以下默认配置：

- **用户名**: `superadmin`
- **邮箱**: `<EMAIL>`
- **默认密码**: `Admin123!`
- **角色**: `超级管理员`
- **权限**: 所有系统权限

## 创建的权限列表

脚本会自动创建以下基础权限：

### 用户管理权限
- `user.read` - 查看用户
- `user.create` - 创建用户
- `user.update` - 更新用户
- `user.delete` - 删除用户

### 角色管理权限
- `role.read` - 查看角色
- `role.create` - 创建角色
- `role.update` - 更新角色
- `role.delete` - 删除角色

### 权限管理权限
- `permission.read` - 查看权限
- `permission.assign` - 分配权限

### 系统管理权限
- `system.admin` - 系统管理

### 对话管理权限
- `chat.read` - 查看对话
- `chat.create` - 创建对话
- `chat.update` - 更新对话
- `chat.delete` - 删除对话

### 模型管理权限
- `model.read` - 查看模型
- `model.create` - 创建模型
- `model.update` - 更新模型
- `model.delete` - 删除模型

## 安全建议

1. **立即修改默认密码**: 首次登录后请立即修改默认密码
2. **使用强密码**: 在生产环境中使用复杂的密码
3. **环境变量**: 建议通过环境变量设置密码而不是硬编码
4. **定期审查**: 定期检查和更新权限配置
5. **备份数据库**: 在运行脚本前备份数据库

## 故障排除

### 常见问题

**Q: 提示"超级管理员已存在"怎么办？**
A: 使用 `--force` 参数强制重新初始化，或者手动删除现有的超级管理员账号。

**Q: 脚本执行失败怎么办？**
A: 检查以下几点：
- 确保数据库文件存在且可写
- 确保所有依赖包已安装
- 检查控制台错误信息

**Q: 如何修改默认配置？**
A: 编辑 `scripts/init-admin.js` 文件中的 `SUPER_ADMIN_CONFIG` 对象。

### 错误代码

- **退出码 1**: 初始化过程中发生错误
- **正常退出**: 初始化成功完成

## 开发说明

### 脚本结构

```
init-admin.js
├── 导入依赖
├── 配置常量
├── createSuperAdmin() - 创建超级管理员用户
├── createSuperAdminRole() - 创建超级管理员角色
├── createBasicPermissions() - 创建基础权限
├── assignUserRole() - 分配用户角色
├── assignAllPermissionsToRole() - 分配权限给角色
├── initializeSuperAdmin() - 主初始化函数
├── parseArguments() - 解析命令行参数
└── main() - 程序入口
```

### 数据库表结构

脚本操作以下数据库表：
- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `user_roles` - 用户角色关联表
- `role_permissions` - 角色权限关联表

## 更新日志

### v1.0.0
- 初始版本
- 支持创建超级管理员账号
- 支持创建基础权限系统
- 支持命令行参数
- 完整的错误处理和日志输出