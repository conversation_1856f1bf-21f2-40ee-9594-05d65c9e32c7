'use client';

import React, { useState, useCallback, useRef, useEffect, Suspense } from 'react';
import { 
  useConversationManager, 
  useChatMessages, 
  useChatStyle,
  useUrlHandler,
  useMessageLoader,
  useConversationEventHandlers,
} from './hooks';
import { Sidebar } from '../Sidebar';
import { ChatContainer } from './components';
import { streamingChatService } from './services/streamingChatService';
import { AgentWithRelations } from '../agents/types';
import { PageLoading } from '../../components/Loading';

type SelectorMode = 'model' | 'agent';

// 内部组件，使用useSearchParams
function SimpleChatPageContent() {
  const {
    conversations,
    currentConversation,
    loading: conversationLoading,
    error: conversationError,
    loadConversations,
    loadConversationsIfNeeded,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
  } = useConversationManager();

  const {
    messages,
    setMessages,
    inputMessage,
    setInputMessage,
    isStreaming,
    setIsStreaming,
    selectedModel,
    setSelectedModel,
    models,
    expandedThinkingMessages,
    enableTools,
    setEnableTools,
    selectedTools,
    setSelectedTools,
    setActiveTool,
    setToolCalls,
    setCurrentAssistantMessageId,
    systemPrompt,
    selectAgent,
    toggleThinkingExpand,
    stopGeneration,
    selectBestModel,
    setAbortController,
  } = useChatMessages();

  const { chatStyle, displaySize, setChatStyle: handleChatStyleChange, setDisplaySize: handleDisplaySizeChange } = useChatStyle();

  // UI状态
  const [error, setError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true); // 初始显示加载界面，避免闪屏
  
  // Agent related state
  const [agents, setAgents] = useState<AgentWithRelations[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<number | null>(null);
  const [selectorMode, setSelectorMode] = useState<SelectorMode>('model');
  
  // 记忆面板状态
  const [isMemoryVisible, setIsMemoryVisible] = useState(false);
  
  // 记忆面板切换函数
  const handleMemoryToggle = useCallback(() => {
    setIsMemoryVisible(prev => !prev);
  }, []);

  // 优化：直接使用models数据，无需转换
  // models现在是CustomModel[]类型，包含完整的display_name等信息
  
  // 为组件兼容性生成customModels格式
  const [customModels, setCustomModels] = useState<Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>>([]);

  // 从CustomModel[]生成customModels显示信息
  useEffect(() => {
    if (models.length > 0) {
      const formattedCustomModels = models.map(model => ({
        base_model: model.base_model,
        display_name: model.display_name, // 使用正确的display_name
        family: model.family,
      }));
      setCustomModels(formattedCustomModels);
      console.log('✅ 生成customModels显示信息:', formattedCustomModels.length, '个模型');
    }
  }, [models]);

  // 使用ref来获取最新的messages值，避免在useCallback依赖中包含messages
  const messagesRef = useRef(messages);
  const selectedModelRef = useRef(selectedModel);
  const setMessagesRef = useRef(setMessages);
  const setActiveToolRef = useRef(setActiveTool);
  const setToolCallsRef = useRef(setToolCalls);
  const setCurrentAssistantMessageIdRef = useRef(setCurrentAssistantMessageId);
  const setIsStreamingRef = useRef(setIsStreaming);
  const setErrorRef = useRef(setError);
  const loadConversationsRef = useRef(loadConversations);
  const systemPromptRef = useRef(systemPrompt);
  const cleanupHandlersRef = useRef<Array<() => void>>([]);

  useEffect(() => {
    messagesRef.current = messages;
    selectedModelRef.current = selectedModel;
    setMessagesRef.current = setMessages;
    setActiveToolRef.current = setActiveTool;
    setToolCallsRef.current = setToolCalls;
    setCurrentAssistantMessageIdRef.current = setCurrentAssistantMessageId;
    setIsStreamingRef.current = setIsStreaming;
    setErrorRef.current = setError;
    loadConversationsRef.current = loadConversations;
    systemPromptRef.current = systemPrompt;
  }, [messages, selectedModel, loadConversations, systemPrompt]); // 移除setState函数作为依赖项

  // 🔧 修复：组件卸载时清理所有pending的更新
  useEffect(() => {
    return () => {
      cleanupHandlersRef.current.forEach(cleanup => cleanup());
      cleanupHandlersRef.current = [];
    };
  }, []);

  // 管理初始化状态，平衡加载体验和防闪屏
  useEffect(() => {
    if (models.length > 0) {
      // 模型已加载，短暂延迟后隐藏加载界面，确保组件稳定
      const timer = setTimeout(() => {
        setIsInitializing(false);
      }, 200); // 200ms延迟，避免闪屏
      
      return () => clearTimeout(timer);
    } else {
      // 模型未加载，设置最大等待时间
      const timeout = setTimeout(() => {
        setIsInitializing(false);
      }, 2000); // 最多等待2秒
      
      return () => clearTimeout(timeout);
    }
  }, [models.length]);

  // 从数据库数据更新消息的辅助函数
  const updateMessagesFromDatabase = useCallback((dbMessages: any[]) => {
    console.log('🔧 更新消息数据，总数:', dbMessages.length);
    
    // 使用与useMessageLoader相同的逻辑处理工具调用消息
    const allMessages = dbMessages.sort((a: any, b: any) => {
      if (a.timestamp !== b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return a.id - b.id;
    });
    
    const formattedMessages: any[] = [];
    const toolCallMessages: any[] = [];
    
    for (const msg of allMessages) {
      if (msg.role === 'tool_call' && msg.tool_name) {
        // 处理工具调用消息
        let args = {};
        let result = '';
        
        try {
          args = msg.tool_args ? JSON.parse(msg.tool_args) : {};
        } catch (e) {
          args = {};
        }
        
        try {
          result = msg.tool_result ? 
            (typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result)) 
            : '';
        } catch (e) {
          result = msg.tool_result || '';
        }
        
        const toolCall = {
          id: `tool-${msg.id}`,
          toolName: msg.tool_name,
          args: args,
          status: msg.tool_status || 'completed',
          result: result,
          error: msg.tool_error || undefined,
          startTime: msg.timestamp || new Date(msg.created_at).getTime(),
          executionTime: msg.tool_execution_time || 0,
        };
        
        toolCallMessages.push(toolCall);
        
        formattedMessages.push({
          id: `tool-placeholder-${msg.id}`,
          role: 'tool_call' as any,
          content: '',
          timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
          toolCall: toolCall,
        });
      } else {
        formattedMessages.push({
          id: `msg-${msg.id}`,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
          model: msg.model,
          // 包含统计字段
          total_duration: msg.total_duration,
          load_duration: msg.load_duration,
          prompt_eval_count: msg.prompt_eval_count,
          prompt_eval_duration: msg.prompt_eval_duration,
          eval_count: msg.eval_count,
          eval_duration: msg.eval_duration,
        });
      }
    }
    
    // 检查是否有统计信息
    const hasStats = formattedMessages.some((msg: any) => 
      msg.role === 'assistant' && (msg.total_duration || msg.eval_count)
    );
    console.log('🔧 更新后的消息是否包含统计信息:', hasStats);
    console.log('🔧 更新后的工具调用数量:', toolCallMessages.length);
    
    setMessagesRef.current(formattedMessages);
    setToolCallsRef.current(toolCallMessages);
  }, []);

  // 处理模型切换，传递对话ID以保存对话特定的模型选择
  const handleModelChange = (modelName: string) => {
    const conversationId = currentConversation?.id;
    setSelectedModel(modelName, conversationId);
  };

  // Fetch agents - 优化：添加缓存和错误处理
  useEffect(() => {
    let isMounted = true;
    
    const fetchAgents = async () => {
      try {
        console.log('🤖 开始加载智能体列表');
        const response = await fetch('/api/agents');
        if (response.ok && isMounted) {
          const agents: AgentWithRelations[] = await response.json();
          setAgents(agents);
          console.log(`✅ 成功加载 ${agents.length} 个智能体`);
        }
      } catch (error) {
        if (isMounted) {
          console.error('❌ 加载智能体失败:', error);
        }
      }
    };

    fetchAgents();
    
    return () => {
      isMounted = false;
    };
  }, []);

  const handleAgentChange = (agentId: number | null) => {
    setSelectedAgentId(agentId);
    selectAgent(agentId);
  };

  const handleSelectorModeChange = (mode: SelectorMode) => {
    setSelectorMode(mode);
  };

  // 使用 URL 处理器
  useUrlHandler({
    models,
    selectedModel,
    currentConversation,
    conversationLoading,
    createConversation,
    switchConversation,
    setSelectedModel,
  });

  // 使用消息加载器
  useMessageLoader({
    currentConversation,
    setSelectedModel,
    setMessages,
    setToolCalls,
    selectedModel,
    models,
    selectBestModel,
  });

  // 使用对话事件处理器
  useConversationEventHandlers({
    currentConversation,
    conversations,
    selectedModel,
    createConversation,
    switchConversation,
    deleteConversation,
    loadConversations,
    setMessages,
    setToolCalls,
    setSelectedModel,
    setError,
    setIsProcessingUrl: () => {}, // 暂时提供一个空函数
  });

  // 清空当前对话
  const clearCurrentChat = useCallback(async () => {
    if (!currentConversation) return;
    
    try {
      const response = await fetch(`/api/conversations/${currentConversation.id}/clear`, {
        method: 'POST',
      });
      
      if (response.ok) {
        // 清空当前消息
        setMessages([]);
        setToolCalls([]);
        setActiveTool(null);
        setError(null);
        
        // 重新加载对话列表
        loadConversations();
      }
    } catch (error) {
      console.error('清空对话失败:', error);
      setError('清空对话失败');
    }
  }, [currentConversation, setMessages, setToolCalls, setActiveTool, setError, loadConversations]);

  // 创建流处理器句柄 - 修复：使用useCallback确保函数稳定性，移除不必要的依赖
  const createStreamHandlers = useCallback(() => {
    const handlers = {
      onMessageUpdate: (messageId: string, content: string, stats?: any) => {
        setMessagesRef.current(prev => 
          prev.map(msg => 
            msg.id === messageId 
              ? { ...msg, content, ...(stats || {}) }
              : msg
          )
        );
      },
      onToolCallStart: (toolCall: any) => {
        setActiveToolRef.current(toolCall);
        setToolCallsRef.current(prev => [...prev, toolCall]);
        
        const toolCallMessage = {
          id: `tool-runtime-${toolCall.id}`,
          role: 'tool_call' as const,
          content: '',
          timestamp: Date.now(),
          toolCall: toolCall,
        };
        setMessagesRef.current(prev => [...prev, toolCallMessage]);
      },
      onToolCallComplete: (toolCallId: string, toolName: string, result: string, executionTime?: number) => {
        setActiveToolRef.current(null);
        
        setToolCallsRef.current(prev => 
          prev.map(tc => {
            const isMatch = toolCallId 
              ? tc.id === toolCallId
              : tc.toolName === toolName && tc.status === 'executing';
            
            return isMatch
              ? { 
                  ...tc, 
                  status: 'completed' as const,
                  result: typeof result === 'string' ? result : JSON.stringify(result),
                  executionTime: executionTime || (Date.now() - tc.startTime)
                }
              : tc;
          })
        );
        
        setMessagesRef.current(prev => 
          prev.map(msg => {
            if (msg.role === 'tool_call' && msg.toolCall) {
              const isMatch = toolCallId 
                ? msg.toolCall.id === toolCallId
                : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';
              
              if (isMatch) {
                return {
                  ...msg,
                  toolCall: {
                    id: msg.toolCall.id,
                    toolName: msg.toolCall.toolName,
                    args: msg.toolCall.args,
                    status: 'completed' as const,
                    result: typeof result === 'string' ? result : JSON.stringify(result),
                    startTime: msg.toolCall.startTime,
                    executionTime: executionTime || (Date.now() - msg.toolCall.startTime)
                  }
                };
              }
            }
            return msg;
          })
        );
      },
      onToolCallError: (toolCallId: string, toolName: string, error: string, executionTime?: number) => {
        setActiveToolRef.current(null);
        
        setToolCallsRef.current(prev => 
          prev.map(tc => {
            const isMatch = toolCallId 
              ? tc.id === toolCallId
              : tc.toolName === toolName && tc.status === 'executing';
            
            return isMatch
              ? { 
                  ...tc, 
                  status: 'error' as const,
                  error: error || '工具调用失败',
                  executionTime: executionTime || (Date.now() - tc.startTime)
                }
              : tc;
          })
        );
        
        setMessagesRef.current(prev => 
          prev.map(msg => {
            if (msg.role === 'tool_call' && msg.toolCall) {
              const isMatch = toolCallId 
                ? msg.toolCall.id === toolCallId
                : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';
              
              if (isMatch) {
                return {
                  ...msg,
                  toolCall: {
                    id: msg.toolCall.id,
                    toolName: msg.toolCall.toolName,
                    args: msg.toolCall.args,
                    status: 'error' as const,
                    error: error || '工具调用失败',
                    startTime: msg.toolCall.startTime,
                    executionTime: executionTime || (Date.now() - msg.toolCall.startTime)
                  }
                };
              }
            }
            return msg;
          })
        );
      },
      onNewAssistantMessage: (messageId: string) => {
        const newAssistantMessage = {
          id: messageId,
          role: 'assistant' as const,
          content: '',
          timestamp: Date.now(),
          model: selectedModelRef.current,
        };
        
        setMessagesRef.current(prev => [...prev, newAssistantMessage]);
        setCurrentAssistantMessageIdRef.current(messageId);
      },
      onTitleUpdate: (conversationId: number, title: string) => {
        console.log('📝 处理标题更新事件:', conversationId, title);
        // 调用updateConversationTitle更新前端状态
        updateConversationTitle(conversationId, title);
      },
      onStreamEnd: () => {
        setIsStreamingRef.current(false);
        setActiveToolRef.current(null);
        setCurrentAssistantMessageIdRef.current(null);
        
        // 优化：更智能的统计信息获取策略，减少API调用
        const cleanup = () => {
          if (currentConversation) {
            console.log('🔧 对话完成，准备获取统计信息');
            
            // 使用单次调用获取统计信息，如果没有则等待后重试一次
            const fetchStats = async (retryOnce = true) => {
              try {
                const token = localStorage.getItem('accessToken');
                const response = await fetch(`/api/conversations/${currentConversation.id}`, {
                  headers: {
                    'Authorization': `Bearer ${token}`,
                  },
                });
                const data = await response.json();
                
                if (data.success && data.messages) {
                  const hasStats = data.messages.some((msg: any) => 
                    msg.role === 'assistant' && (msg.total_duration || msg.eval_count)
                  );
                  
                  if (hasStats) {
                    console.log('✅ 获取到统计信息，更新消息');
                    updateMessagesFromDatabase(data.messages);
                  } else if (retryOnce) {
                    console.log('⏳ 统计信息未就绪，1秒后重试一次');
                    setTimeout(() => fetchStats(false), 1000);
                  } else {
                    console.log('⚠️ 统计信息仍未就绪，使用当前消息');
                    updateMessagesFromDatabase(data.messages);
                  }
                } else {
                  console.log('❌ 获取消息数据失败');
                }
              } catch (err) {
                console.error('获取统计信息失败:', err);
              }
            };
            
            // 延迟300ms后开始获取，给服务器时间保存统计信息
            setTimeout(() => fetchStats(), 300);
          } else {
            console.log('🔄 无当前对话，刷新对话列表');
            loadConversationsRef.current();
          }
        };
        
        // 添加到清理队列
        cleanupHandlersRef.current.push(cleanup);
        cleanup();
        
        // 从清理队列中移除
        setTimeout(() => {
          const index = cleanupHandlersRef.current.indexOf(cleanup);
          if (index > -1) {
            cleanupHandlersRef.current.splice(index, 1);
          }
        }, 3000);
      },
      onError: (errorMessage: string) => {
        setErrorRef.current(errorMessage);
        setIsStreamingRef.current(false);
      },
    };
    return handlers;
  }, [updateMessagesFromDatabase, currentConversation, updateConversationTitle]);

  // 插入文本到输入框
  const handleInsertText = useCallback((text: string) => {
    setInputMessage(text);
  }, [setInputMessage]);

  // 发送消息的核心逻辑
  const sendMessage = useCallback(async () => {
    if (!inputMessage.trim() || !selectedModel || isStreaming) {
      return;
    }

    let activeConversation = currentConversation;
    if (!activeConversation) {
      const title = inputMessage.trim().substring(0, 30) + (inputMessage.length > 30 ? '...' : '');
      const conversationId = await createConversation(title, selectedModel);
      if (!conversationId) {
        setError('创建对话失败');
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
      activeConversation = currentConversation;
    }

    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content: inputMessage.trim(),
      timestamp: Date.now(),
    };

    // 获取当前的消息列表（使用ref避免在依赖中包含messages）
    const currentMessages = messagesRef.current;
    
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsStreaming(true);
    setError(null);
    setToolCalls([]);
    setActiveTool(null);

    const assistantMessageId = `assistant-${Date.now()}`;
    const assistantMessage = {
      id: assistantMessageId,
      role: 'assistant' as const,
      content: '',
      timestamp: Date.now(),
      model: selectedModel,
    };

    setMessages(prev => [...prev, assistantMessage]);
    setCurrentAssistantMessageId(assistantMessageId);

    try {
      // 创建新的 AbortController
      const controller = new AbortController();
      setAbortController(controller);

      // 获取标题总结设置
      let titleSummarySettings = { enabled: false, model: '' };
      try {
        const savedSettings = localStorage.getItem('prompt_optimize_settings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          titleSummarySettings = {
            enabled: settings.titleSummaryEnabled || false,
            model: settings.titleSummaryModel || ''
          };
        }
      } catch (error) {
        console.error('Failed to load title summary settings:', error);
      }

      const chatRequestBody = {
        model: selectedModel,
        conversationId: activeConversation?.id,
        agentId: selectedAgentId,
        messages: [
          ...(systemPromptRef.current ? [{ role: 'system', content: systemPromptRef.current }] : []),
          ...currentMessages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          {
            role: 'user',
            content: userMessage.content,
          },
        ],
        stream: true,
        enableTools,
        selectedTools,
        titleSummarySettings,
      };

      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(chatRequestBody),
        signal: controller.signal, // 添加中断信号
      });

      if (!response.ok) {
        throw new Error('聊天请求失败');
      }

      // 使用流式服务处理响应，传递 AbortController
      await streamingChatService.processStreamingResponse(response, createStreamHandlers(), assistantMessageId, controller);

    } catch (err) {
      // 如果是中断错误，不显示错误信息
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🛑 用户主动停止了生成');
      } else {
        setError(err instanceof Error ? err.message : '发送消息失败');
        setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
      }
    } finally {
      setIsStreaming(false);
      setAbortController(null);
    }
  }, [
    inputMessage, selectedModel, isStreaming, currentConversation,
    enableTools, selectedTools, createConversation, setAbortController,
    createStreamHandlers, setActiveTool, setCurrentAssistantMessageId, setInputMessage,
    setIsStreaming, setMessages, setToolCalls
  ]);

  // 侧边栏事件处理函数 - 优化：按需加载对话列表
  const handleCreateConversation = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/simple-chat?new=true';
    }
  };

  const handleLoadConversation = async (conversationId: number) => {
    // 确保有对话列表数据
    await loadConversationsIfNeeded();
    if (typeof window !== 'undefined') {
      window.location.href = `/simple-chat?id=${conversationId}`;
    }
  };

  const handleDeleteConversation = async (conversationId: number) => {
    try {
      // 确保有对话列表数据
      await loadConversationsIfNeeded();
      
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        await loadConversations(); // 删除后刷新列表
        
        // 如果删除的是当前对话，重定向到新对话
        if (currentConversation?.id === conversationId) {
          if (typeof window !== 'undefined') {
            window.location.href = '/simple-chat?new=true';
          }
        }
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };

  // 确保侧边栏有对话列表数据
  useEffect(() => {
    // 延迟加载对话列表，避免阻塞页面初始化
    const timer = setTimeout(() => {
      loadConversationsIfNeeded();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [loadConversationsIfNeeded]);

  return (
    <div className="flex h-screen bg-theme-background-secondary dark:bg-theme-background overflow-hidden">
      {/* 侧边栏 */}
      <Sidebar
        conversations={conversations}
        currentConversation={currentConversation}
        onCreateConversation={handleCreateConversation}
        onLoadConversation={handleLoadConversation}
        onDeleteConversation={handleDeleteConversation}
      />

      {/* 主聊天区域 */}
      {isInitializing ? (
        <div className="flex-1 overflow-auto">
          <PageLoading 
            text="loading"
            fullScreen={true}
          />
        </div>
      ) : (
        <ChatContainer
          currentConversation={currentConversation}
          models={models}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          agents={agents}
          selectedAgentId={selectedAgentId}
          onAgentChange={handleAgentChange}
          selectorMode={selectorMode}
          onSelectorModeChange={handleSelectorModeChange}
          customModels={customModels}
          messages={messages}
          inputMessage={inputMessage}
          onInputChange={setInputMessage}
          onSendMessage={sendMessage}
          isStreaming={isStreaming}
          onStopGeneration={stopGeneration}
          expandedThinkingMessages={expandedThinkingMessages}
          onToggleThinkingExpand={toggleThinkingExpand}
          enableTools={enableTools}
          selectedTools={selectedTools}
          onToolsToggle={setEnableTools}
          onSelectedToolsChange={setSelectedTools}
          onInsertText={handleInsertText}
          onClearChat={clearCurrentChat}
          error={error}
          onDismissError={() => setError(null)}
          chatStyle={chatStyle}
          displaySize={displaySize}
          onChatStyleChange={handleChatStyleChange}
          onDisplaySizeChange={handleDisplaySizeChange}
          isMemoryVisible={isMemoryVisible}
          onMemoryToggle={handleMemoryToggle}
        />
      )}
    </div>
  );
}

// 外部组件，用Suspense包装内部组件
export default function SimpleChatPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen bg-background">
        <PageLoading 
          text="loading"
          fullScreen={true}
        />
      </div>
    }>
      <SimpleChatPageContent />
    </Suspense>
  );
}