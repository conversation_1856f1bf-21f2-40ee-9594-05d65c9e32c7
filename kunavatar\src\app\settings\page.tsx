'use client';

import React, { useState, useEffect } from 'react';
import { usePromptOptimizeSettings, useAvailableModels, useUserPermissions } from './hooks';
import { Sidebar } from '../Sidebar';
import { Conversation } from '@/lib/database';
import { SettingsTabs, AssistantModelTab, AccountManagementTab, UserManagementTab, AppearanceTab } from './components';
import { NotificationProvider, NotificationContainer, useNotification } from '@/components/notification';
import { PageLoading } from '@/components/Loading';

// 连接到Context的通知容器
function ConnectedNotificationContainer() {
  const { notifications, dismiss } = useNotification();
  return (
    <NotificationContainer 
      notifications={notifications} 
      onDismiss={dismiss} 
      position="top-right"
      maxNotifications={5}
    />
  );
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const { settings, isLoaded, updateSetting } = usePromptOptimizeSettings();
  const { models: availableModels, isLoading: modelsLoading, error: modelsError } = useAvailableModels();
  const { isAdmin, loading: permissionsLoading } = useUserPermissions();

  // 当模型列表加载完成且设置已加载时，自动设置默认模型
  useEffect(() => {
    if (isLoaded && !modelsLoading && availableModels.length > 0) {
      const firstModel = availableModels[0].value;
      
      // 如果当前模型为空或不在可用模型列表中，设置为第一个可用模型
      if (!settings.promptModel || !availableModels.find(m => m.value === settings.promptModel)) {
        updateSetting('promptModel', firstModel);
      }
      if (!settings.titleSummaryModel || !availableModels.find(m => m.value === settings.titleSummaryModel)) {
        updateSetting('titleSummaryModel', firstModel);
      }
      if (!settings.memoryModel || !availableModels.find(m => m.value === settings.memoryModel)) {
        updateSetting('memoryModel', firstModel);
      }
    }
  }, [isLoaded, modelsLoading, availableModels, settings, updateSetting]);

  // 如果当前标签页是用户管理但用户不是管理员，切换到账户管理
  useEffect(() => {
    if (!permissionsLoading && activeTab === 'users' && !isAdmin) {
      setActiveTab('account');
    }
  }, [isAdmin, permissionsLoading, activeTab]);
  
  // 简化的侧边栏事件处理 - 不需要conversations数据
  const handleCreateConversation = () => {
    window.location.href = '/simple-chat?new=true';
  };

  const handleLoadConversation = (conversationId: number) => {
    window.location.href = `/simple-chat?id=${conversationId}`;
  };

  const handleDeleteConversation = async (conversationId: number) => {
    // 空实现，设置页面不处理对话删除
  };

  // 如果设置还未加载完成，显示加载状态
  if (!isLoaded || permissionsLoading) {
    return (
      <div className="flex h-screen bg-theme-background">
        <Sidebar
          conversations={[]}
          currentConversation={null}
          onCreateConversation={handleCreateConversation}
          onLoadConversation={handleLoadConversation}
          onDeleteConversation={handleDeleteConversation}
        />
        <div className="flex-1 overflow-auto">
          <PageLoading 
            text="正在加载设置..."
            fullScreen={true}
          />
        </div>
      </div>
    );
  }

  return (
    <NotificationProvider>
      <div className="flex h-screen bg-theme-background">
        <Sidebar
          conversations={[]}
          currentConversation={null}
          onCreateConversation={handleCreateConversation}
          onLoadConversation={handleLoadConversation}
          onDeleteConversation={handleDeleteConversation}
        />
        <div className="flex-1 overflow-auto scrollbar-thin">
          <div className="min-h-screen bg-theme-background transition-colors duration-300">
            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
              <div className="px-4 py-6 sm:px-0">
                <h1 className="text-2xl font-bold mb-6 text-theme-foreground">设置</h1>
                
                {/* 标签页导航 */}
                <SettingsTabs activeTab={activeTab} onTabChange={setActiveTab} isAdmin={isAdmin} />

                {/* Tab内容区 */}
                {activeTab === 'account' && (
                  <AccountManagementTab />
                )}
                {activeTab === 'users' && isAdmin && (
                  <UserManagementTab />
                )}
                {activeTab === 'assistant' && (
                  <AssistantModelTab
                    settings={settings}
                    availableModels={availableModels}
                    modelsLoading={modelsLoading}
                    modelsError={modelsError}
                    onUpdateSetting={updateSetting}
                  />
                )}
                {activeTab === 'appearance' && (
                  <AppearanceTab />
                )}
              </div>
            </main>
          </div>
        </div>
      </div>
      {/* 通知容器 */}
      <ConnectedNotificationContainer />
    </NotificationProvider>
  );
}